# Environment Variables Example
# Copy this file to .env.local and fill in your actual values

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://yourwebsite.com

# Contact Form (if using a service like Formspree, EmailJS, etc.)
NEXT_PUBLIC_CONTACT_FORM_ENDPOINT=your_form_endpoint_here

# Analytics (optional)
NEXT_PUBLIC_GA_TRACKING_ID=your_google_analytics_id
NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id

# Social Media (for dynamic links)
NEXT_PUBLIC_GITHUB_USERNAME=yourusername
NEXT_PUBLIC_LINKEDIN_USERNAME=yourusername
NEXT_PUBLIC_TWITTER_USERNAME=yourusername

# Email Configuration (if using server-side email)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Database (if adding a blog or CMS)
DATABASE_URL=your_database_url_here

# CMS (if using Sanity, Contentful, etc.)
SANITY_PROJECT_ID=your_sanity_project_id
SANITY_DATASET=production
SANITY_API_TOKEN=your_sanity_token

# Vercel (automatically set in production)
VERCEL_URL=your-vercel-url.vercel.app
