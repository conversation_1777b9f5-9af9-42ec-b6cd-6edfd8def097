module.exports = {

"[next]/internal/font/google/inter_5802845b.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "inter_5802845b-module__9kuUBG__className",
  "variable": "inter_5802845b-module__9kuUBG__variable",
});
}}),
"[next]/internal/font/google/inter_5802845b.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_5802845b.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Inter', 'Inter Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/fira_code_b2eb6785.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "fira_code_b2eb6785-module__U72mxW__className",
  "variable": "fira_code_b2eb6785-module__U72mxW__variable",
});
}}),
"[next]/internal/font/google/fira_code_b2eb6785.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$fira_code_b2eb6785$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/fira_code_b2eb6785.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$fira_code_b2eb6785$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Fira Code', 'Fira Code Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$fira_code_b2eb6785$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$fira_code_b2eb6785$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[project]/src/components/providers/theme-provider.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const ThemeProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/providers/theme-provider.tsx <module evaluation>", "ThemeProvider");
}}),
"[project]/src/components/providers/theme-provider.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const ThemeProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/providers/theme-provider.tsx", "ThemeProvider");
}}),
"[project]/src/components/providers/theme-provider.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$theme$2d$provider$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/providers/theme-provider.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$theme$2d$provider$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/providers/theme-provider.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$theme$2d$provider$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/components/common/scroll-progress.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ScrollProgress": (()=>ScrollProgress)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const ScrollProgress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ScrollProgress() from the server but ScrollProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/common/scroll-progress.tsx <module evaluation>", "ScrollProgress");
}}),
"[project]/src/components/common/scroll-progress.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ScrollProgress": (()=>ScrollProgress)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const ScrollProgress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ScrollProgress() from the server but ScrollProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/common/scroll-progress.tsx", "ScrollProgress");
}}),
"[project]/src/components/common/scroll-progress.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$scroll$2d$progress$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/common/scroll-progress.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$scroll$2d$progress$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/common/scroll-progress.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$scroll$2d$progress$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/data/social.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "navigationItems": (()=>navigationItems),
    "personalInfo": (()=>personalInfo),
    "socialLinks": (()=>socialLinks)
});
const socialLinks = [
    {
        name: "GitHub",
        url: "https://github.com/bhupenderyadav",
        icon: "Github"
    },
    {
        name: "LinkedIn",
        url: "https://linkedin.com/in/bhupenderyadav",
        icon: "Linkedin"
    },
    {
        name: "Twitter",
        url: "https://twitter.com/bhupenderyadav",
        icon: "Twitter"
    },
    {
        name: "Email",
        url: "mailto:<EMAIL>",
        icon: "Mail"
    },
    {
        name: "Instagram",
        url: "https://instagram.com/bhupenderyadav",
        icon: "Instagram"
    },
    {
        name: "YouTube",
        url: "https://youtube.com/@bhupenderyadav",
        icon: "Youtube"
    }
];
const navigationItems = [
    {
        name: "Home",
        href: "#home",
        icon: "Home"
    },
    {
        name: "About",
        href: "#about",
        icon: "User"
    },
    {
        name: "Skills",
        href: "#skills",
        icon: "Code"
    },
    {
        name: "Projects",
        href: "#projects",
        icon: "FolderOpen"
    },
    {
        name: "YouTube",
        href: "#youtube",
        icon: "Youtube"
    },
    {
        name: "Experience",
        href: "#experience",
        icon: "Briefcase"
    },
    {
        name: "Contact",
        href: "#contact",
        icon: "MessageCircle"
    }
];
const personalInfo = {
    name: "Bhupender Yadav",
    title: "Full Stack Developer",
    subtitle: "Building digital experiences that matter",
    bio: "I'm a passionate full stack developer with 5+ years of experience creating modern web applications. I love turning complex problems into simple, beautiful, and intuitive solutions.",
    location: "India",
    email: "<EMAIL>",
    phone: "+91 7206110977",
    website: "https://bhupenderyadav.com",
    resumeUrl: "/resume.pdf",
    avatar: "/bhupi.jpg"
};
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_5802845b.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$fira_code_b2eb6785$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/fira_code_b2eb6785.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$theme$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/providers/theme-provider.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$scroll$2d$progress$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/scroll-progress.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/social.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
const metadata = {
    title: {
        default: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].name} - ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].title}`,
        template: `%s | ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].name}`
    },
    description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].bio,
    keywords: [
        "Full Stack Developer",
        "React",
        "Next.js",
        "TypeScript",
        "Node.js",
        "Web Development",
        "Frontend",
        "Backend",
        "Portfolio"
    ],
    authors: [
        {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].name,
            url: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].website
        }
    ],
    creator: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].name,
    openGraph: {
        type: "website",
        locale: "en_US",
        url: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].website,
        title: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].name} - ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].title}`,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].bio,
        siteName: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].name,
        images: [
            {
                url: "/og-image.jpg",
                width: 1200,
                height: 630,
                alt: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].name} - ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].title}`
            }
        ]
    },
    twitter: {
        card: "summary_large_image",
        title: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].name} - ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].title}`,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$social$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["personalInfo"].bio,
        images: [
            "/og-image.jpg"
        ],
        creator: "@yourusername"
    },
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            "max-video-preview": -1,
            "max-image-preview": "large",
            "max-snippet": -1
        }
    },
    verification: {
        google: "your-google-verification-code"
    }
};
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        suppressHydrationWarning: true,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
            className: `${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$fira_code_b2eb6785$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} font-sans antialiased`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$providers$2f$theme$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ThemeProvider"], {
                attribute: "class",
                defaultTheme: "system",
                enableSystem: true,
                disableTransitionOnChange: true,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$scroll$2d$progress$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ScrollProgress"], {}, void 0, false, {
                        fileName: "[project]/src/app/layout.tsx",
                        lineNumber: 94,
                        columnNumber: 11
                    }, this),
                    children
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 88,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/layout.tsx",
            lineNumber: 85,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/layout.tsx",
        lineNumber: 84,
        columnNumber: 5
    }, this);
}
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-rsc] (ecmascript)").vendored['react-rsc'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__63a93501._.js.map