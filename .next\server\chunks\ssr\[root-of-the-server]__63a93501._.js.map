{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_5802845b.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_5802845b-module__9kuUBG__className\",\n  \"variable\": \"inter_5802845b-module__9kuUBG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_5802845b.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-inter%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/fira_code_b2eb6785.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"fira_code_b2eb6785-module__U72mxW__className\",\n  \"variable\": \"fira_code_b2eb6785-module__U72mxW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/fira_code_b2eb6785.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Fira_Code%22,%22arguments%22:[{%22variable%22:%22--font-fira-code%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22firaCode%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Fira Code', 'Fira Code Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,yJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,yJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,yJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/providers/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/theme-provider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6EACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/providers/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/common/scroll-progress.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ScrollProgress = registerClientReference(\n    function() { throw new Error(\"Attempted to call ScrollProgress() from the server but ScrollProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/scroll-progress.tsx <module evaluation>\",\n    \"ScrollProgress\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2EACA", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/common/scroll-progress.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ScrollProgress = registerClientReference(\n    function() { throw new Error(\"Attempted to call ScrollProgress() from the server but ScrollProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/scroll-progress.tsx\",\n    \"ScrollProgress\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,uDACA", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/data/social.ts"], "sourcesContent": ["import { SocialLink, NavItem } from \"@/types\";\n\nexport const socialLinks: SocialLink[] = [\n  {\n    name: \"GitHub\",\n    url: \"https://github.com/yourusername\",\n    icon: \"Github\"\n  },\n  {\n    name: \"LinkedIn\",\n    url: \"https://linkedin.com/in/yourusername\",\n    icon: \"Linkedin\"\n  },\n  {\n    name: \"Twitter\",\n    url: \"https://twitter.com/yourusername\",\n    icon: \"Twitter\"\n  },\n  {\n    name: \"<PERSON><PERSON>\",\n    url: \"mailto:<EMAIL>\",\n    icon: \"Mail\"\n  },\n  {\n    name: \"Instagram\",\n    url: \"https://instagram.com/yourusername\",\n    icon: \"Instagram\"\n  },\n  {\n    name: \"YouTube\",\n    url: \"https://youtube.com/@yourusername\",\n    icon: \"Youtube\"\n  }\n];\n\nexport const navigationItems: NavItem[] = [\n  {\n    name: \"Home\",\n    href: \"#home\",\n    icon: \"Home\"\n  },\n  {\n    name: \"About\",\n    href: \"#about\",\n    icon: \"User\"\n  },\n  {\n    name: \"Skills\",\n    href: \"#skills\",\n    icon: \"Code\"\n  },\n  {\n    name: \"Projects\",\n    href: \"#projects\",\n    icon: \"FolderOpen\"\n  },\n  {\n    name: \"YouTube\",\n    href: \"#youtube\",\n    icon: \"Youtube\"\n  },\n  {\n    name: \"Experience\",\n    href: \"#experience\",\n    icon: \"Briefcase\"\n  },\n  {\n    name: \"Contact\",\n    href: \"#contact\",\n    icon: \"MessageCircle\"\n  }\n];\n\nexport const personalInfo = {\n  name: \"Your Name\",\n  title: \"Full Stack Developer\",\n  subtitle: \"Building digital experiences that matter\",\n  bio: \"I'm a passionate full stack developer with 5+ years of experience creating modern web applications. I love turning complex problems into simple, beautiful, and intuitive solutions.\",\n  location: \"San Francisco, CA\",\n  email: \"<EMAIL>\",\n  phone: \"+****************\",\n  website: \"https://yourwebsite.com\",\n  resumeUrl: \"/resume.pdf\",\n  avatar: \"/avatar.jpg\"\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,cAA4B;IACvC;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;CACD;AAEM,MAAM,kBAA6B;IACxC;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;CACD;AAEM,MAAM,eAAe;IAC1B,MAAM;IACN,OAAO;IACP,UAAU;IACV,KAAK;IACL,UAAU;IACV,OAAO;IACP,OAAO;IACP,SAAS;IACT,WAAW;IACX,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Inter, Fira_Code } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ThemeProvider } from \"@/components/providers/theme-provider\";\nimport { ScrollProgress } from \"@/components/common/scroll-progress\";\nimport { personalInfo } from \"@/data/social\";\n\nconst inter = Inter({\n  variable: \"--font-inter\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nconst firaCode = Fira_Code({\n  variable: \"--font-fira-code\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: {\n    default: `${personalInfo.name} - ${personalInfo.title}`,\n    template: `%s | ${personalInfo.name}`,\n  },\n  description: personalInfo.bio,\n  keywords: [\n    \"Full Stack Developer\",\n    \"React\",\n    \"Next.js\",\n    \"TypeScript\",\n    \"Node.js\",\n    \"Web Development\",\n    \"Frontend\",\n    \"Backend\",\n    \"Portfolio\",\n  ],\n  authors: [{ name: personalInfo.name, url: personalInfo.website }],\n  creator: personalInfo.name,\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: personalInfo.website,\n    title: `${personalInfo.name} - ${personalInfo.title}`,\n    description: personalInfo.bio,\n    siteName: personalInfo.name,\n    images: [\n      {\n        url: \"/og-image.jpg\",\n        width: 1200,\n        height: 630,\n        alt: `${personalInfo.name} - ${personalInfo.title}`,\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: `${personalInfo.name} - ${personalInfo.title}`,\n    description: personalInfo.bio,\n    images: [\"/og-image.jpg\"],\n    creator: \"@yourusername\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n  verification: {\n    google: \"your-google-verification-code\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body\n        className={`${inter.variable} ${firaCode.variable} font-sans antialiased`}\n      >\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          enableSystem\n          disableTransitionOnChange\n        >\n          <ScrollProgress />\n          {children}\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;;;;;;;;AAcO,MAAM,WAAqB;IAChC,OAAO;QACL,SAAS,GAAG,qHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG,EAAE,qHAAA,CAAA,eAAY,CAAC,KAAK,EAAE;QACvD,UAAU,CAAC,KAAK,EAAE,qHAAA,CAAA,eAAY,CAAC,IAAI,EAAE;IACvC;IACA,aAAa,qHAAA,CAAA,eAAY,CAAC,GAAG;IAC7B,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QAAC;YAAE,MAAM,qHAAA,CAAA,eAAY,CAAC,IAAI;YAAE,KAAK,qHAAA,CAAA,eAAY,CAAC,OAAO;QAAC;KAAE;IACjE,SAAS,qHAAA,CAAA,eAAY,CAAC,IAAI;IAC1B,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,qHAAA,CAAA,eAAY,CAAC,OAAO;QACzB,OAAO,GAAG,qHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG,EAAE,qHAAA,CAAA,eAAY,CAAC,KAAK,EAAE;QACrD,aAAa,qHAAA,CAAA,eAAY,CAAC,GAAG;QAC7B,UAAU,qHAAA,CAAA,eAAY,CAAC,IAAI;QAC3B,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK,GAAG,qHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG,EAAE,qHAAA,CAAA,eAAY,CAAC,KAAK,EAAE;YACrD;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO,GAAG,qHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG,EAAE,qHAAA,CAAA,eAAY,CAAC,KAAK,EAAE;QACrD,aAAa,qHAAA,CAAA,eAAY,CAAC,GAAG;QAC7B,QAAQ;YAAC;SAAgB;QACzB,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,6IAAA,CAAA,UAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC;sBAEzE,cAAA,8OAAC,oJAAA,CAAA,gBAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,YAAY;gBACZ,yBAAyB;;kCAEzB,8OAAC,kJAAA,CAAA,iBAAc;;;;;oBACd;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}