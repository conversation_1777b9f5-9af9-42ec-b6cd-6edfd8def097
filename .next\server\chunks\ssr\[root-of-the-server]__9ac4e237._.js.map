{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\";\nimport { type ThemeProviderProps } from \"next-themes/dist/types\";\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/common/scroll-progress.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion, useScroll, useSpring } from \"framer-motion\";\n\nexport function ScrollProgress() {\n  const { scrollYProgress } = useScroll();\n  const scaleX = useSpring(scrollYProgress, {\n    stiffness: 100,\n    damping: 30,\n    restDelta: 0.001,\n  });\n\n  return (\n    <motion.div\n      className=\"fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary to-purple-600 origin-left z-50\"\n      style={{ scaleX }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAFA;;;AAIO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;QACxC,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,OAAO;YAAE;QAAO;;;;;;AAGtB", "debugId": null}}]}