{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/data/social.ts"], "sourcesContent": ["import { SocialLink, NavItem } from \"@/types\";\n\nexport const socialLinks: SocialLink[] = [\n  {\n    name: \"GitHub\",\n    url: \"https://github.com/bhupenderyadav\",\n    icon: \"Github\"\n  },\n  {\n    name: \"LinkedIn\",\n    url: \"https://linkedin.com/in/bhupenderyadav\",\n    icon: \"Linkedin\"\n  },\n  {\n    name: \"Twitter\",\n    url: \"https://twitter.com/bhupenderyadav\",\n    icon: \"Twitter\"\n  },\n  {\n    name: \"Email\",\n    url: \"mailto:<EMAIL>\",\n    icon: \"Mail\"\n  },\n  {\n    name: \"Instagram\",\n    url: \"https://instagram.com/bhupenderyadav\",\n    icon: \"Instagram\"\n  },\n  {\n    name: \"YouTube\",\n    url: \"https://youtube.com/@bhupenderyadav\",\n    icon: \"Youtube\"\n  }\n];\n\nexport const navigationItems: NavItem[] = [\n  {\n    name: \"Home\",\n    href: \"#home\",\n    icon: \"Home\"\n  },\n  {\n    name: \"About\",\n    href: \"#about\",\n    icon: \"User\"\n  },\n  {\n    name: \"Skills\",\n    href: \"#skills\",\n    icon: \"Code\"\n  },\n  {\n    name: \"Experience\",\n    href: \"#experience\",\n    icon: \"Briefcase\"\n  },\n  {\n    name: \"Projects\",\n    href: \"#projects\",\n    icon: \"FolderOpen\"\n  },\n  {\n    name: \"Resume\",\n    href: \"#resume\",\n    icon: \"FileText\"\n  },\n  {\n    name: \"YouTube\",\n    href: \"#youtube\",\n    icon: \"Youtube\"\n  },\n  {\n    name: \"Contact\",\n    href: \"#contact\",\n    icon: \"MessageCircle\"\n  }\n];\n\nexport const personalInfo = {\n  name: \"Bhupender Yadav\",\n  title: \"Full Stack Developer & UI UX Designer\",\n  subtitle: \"Building digital experiences that matter\",\n  bio: \"Passionate web developer with expertise in building dynamic and responsive websites. Skilled in JavaScript, React js, Tailwind CSS and with a focus on user-friendly design. Enthusiastic about technology, fitness, and content creation.\",\n  location: \"India\",\n  email: \"<EMAIL>\",\n  phone: \"+91 7206110977\",\n  website: \"https://bhupenderyadav.com\",\n  resumeUrl: \"/resume.pdf\",\n  avatar: \"/bhupi.jpg\"\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,cAA4B;IACvC;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;IACA;QACE,MAAM;QACN,KAAK;QACL,MAAM;IACR;CACD;AAEM,MAAM,kBAA6B;IACxC;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;CACD;AAEM,MAAM,eAAe;IAC1B,MAAM;IACN,OAAO;IACP,UAAU;IACV,KAAK;IACL,UAAU;IACV,OAAO;IACP,OAAO;IACP,SAAS;IACT,WAAW;IACX,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Menu, X } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nimport { navigationItems } from \"@/data/social\";\n\nexport function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: \"smooth\" });\n      setIsMobileMenuOpen(false);\n    }\n  };\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className={`fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${\n        isScrolled\n          ? \"bg-background/80 backdrop-blur-md border-b border-border\"\n          : \"bg-transparent\"\n      }`}\n    >\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Desktop Navigation - Centered */}\n          <nav className=\"hidden md:flex items-center space-x-10 lg:space-x-12 mx-auto\">\n            {navigationItems.map((item, index) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                onClick={(e) => {\n                  e.preventDefault();\n                  scrollToSection(item.href);\n                }}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.1 + index * 0.1 }}\n                whileHover={{ scale: 1.2 }}\n                whileTap={{ scale: 0.9 }}\n                className=\"text-base lg:text-lg font-bold text-slate-200 hover:text-white transition-all duration-150 relative group cursor-pointer px-3 py-2\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-1 bg-gradient-to-r from-cyan-400 via-teal-400 to-blue-500 group-hover:w-full transition-all duration-150 rounded-full shadow-lg shadow-cyan-400/50\" />\n              </motion.a>\n            ))}\n          </nav>\n\n          {/* Mobile Menu Button - Right Side */}\n          <motion.div\n            whileHover={{ scale: 1.3 }}\n            whileTap={{ scale: 0.8 }}\n            transition={{ duration: 0.1 }}\n          >\n            <Button\n              variant=\"ghost\"\n              size=\"lg\"\n              className=\"md:hidden ml-auto transition-all duration-150 hover:bg-white/20 border border-white/20 hover:border-cyan-400/50 rounded-xl p-3\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"h-7 w-7 text-white\" />\n              ) : (\n                <Menu className=\"h-7 w-7 text-white\" />\n              )}\n            </Button>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"md:hidden bg-background/95 backdrop-blur-md border-b border-border\"\n          >\n            <nav className=\"container mx-auto px-4 py-6 space-y-6\">\n              {navigationItems.map((item, index) => (\n                <motion.a\n                  key={item.name}\n                  href={item.href}\n                  onClick={(e) => {\n                    e.preventDefault();\n                    scrollToSection(item.href);\n                  }}\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                  whileHover={{ scale: 1.1, x: 15 }}\n                  whileTap={{ scale: 0.9 }}\n                  className=\"block text-lg font-bold text-slate-200 hover:text-white transition-all duration-150 cursor-pointer py-4 px-6 rounded-xl hover:bg-white/10 border border-transparent hover:border-cyan-400/30 hover:shadow-lg hover:shadow-cyan-400/20\"\n                >\n                  {item.name}\n                </motion.a>\n              ))}\n            </nav>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAEA;AAPA;;;;;;;AASO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;YAC5C,oBAAoB;QACtB;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAW,CAAC,4DAA4D,EACtE,aACI,6DACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ,qHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,gBAAgB,KAAK,IAAI;oCAC3B;oCACA,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,MAAM,QAAQ;oCAAI;oCACvC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;oCACvB,WAAU;;wCAET,KAAK,IAAI;sDACV,8OAAC;4CAAK,WAAU;;;;;;;mCAdX,KAAK,IAAI;;;;;;;;;;sCAoBpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAI;4BACzB,UAAU;gCAAE,OAAO;4BAAI;4BACvB,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,oBAAoB,CAAC;0CAEnC,iCACC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACZ,qHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCAEP,MAAM,KAAK,IAAI;gCACf,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,gBAAgB,KAAK,IAAI;gCAC3B;gCACA,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,YAAY;oCAAE,OAAO;oCAAK,GAAG;gCAAG;gCAChC,UAAU;oCAAE,OAAO;gCAAI;gCACvB,WAAU;0CAET,KAAK,IAAI;+BAbL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBhC", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/layout/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Heart, ArrowUp } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { personalInfo, socialLinks, navigationItems } from \"@/data/social\";\n\nexport function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: \"smooth\" });\n    }\n  };\n\n  return (\n    <footer className=\"bg-muted/30 border-t border-border\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"md:col-span-2\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"text-2xl font-bold gradient-text mb-4\">\n                {personalInfo.name}\n              </h3>\n              <p className=\"text-muted-foreground mb-6 max-w-md\">\n                {personalInfo.bio}\n              </p>\n              <div className=\"flex gap-4\">\n                {socialLinks.slice(0, 4).map((link) => (\n                  <motion.a\n                    key={link.name}\n                    href={link.url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    whileHover={{ scale: 1.1, y: -2 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"p-2 rounded-lg bg-background hover:bg-primary hover:text-primary-foreground transition-all duration-300\"\n                  >\n                    <span className=\"sr-only\">{link.name}</span>\n                    {/* Icon placeholder - you can replace with actual icons */}\n                    <div className=\"w-5 h-5 bg-current rounded\" />\n                  </motion.a>\n                ))}\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ delay: 0.1 }}\n            >\n              <h4 className=\"font-semibold mb-4 text-foreground\">Quick Links</h4>\n              <nav className=\"space-y-2\">\n                {navigationItems.map((item) => (\n                  <a\n                    key={item.name}\n                    href={item.href}\n                    onClick={(e) => {\n                      e.preventDefault();\n                      scrollToSection(item.href);\n                    }}\n                    className=\"block text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {item.name}\n                  </a>\n                ))}\n              </nav>\n            </motion.div>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ delay: 0.2 }}\n            >\n              <h4 className=\"font-semibold mb-4 text-foreground\">Get in Touch</h4>\n              <div className=\"space-y-2 text-sm text-muted-foreground\">\n                <p>{personalInfo.email}</p>\n                <p>{personalInfo.phone}</p>\n                <p>{personalInfo.location}</p>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n\n        <Separator className=\"my-8\" />\n\n        {/* Bottom Section */}\n        <div className=\"flex flex-col sm:flex-row items-center justify-between gap-4\">\n          <motion.p\n            initial={{ opacity: 0 }}\n            whileInView={{ opacity: 1 }}\n            viewport={{ once: true }}\n            className=\"text-sm text-muted-foreground flex items-center gap-1\"\n          >\n            © {new Date().getFullYear()} {personalInfo.name}. Made with{\" \"}\n            <Heart className=\"h-4 w-4 text-red-500 fill-current\" /> using Next.js\n          </motion.p>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            whileInView={{ opacity: 1 }}\n            viewport={{ once: true }}\n            transition={{ delay: 0.1 }}\n          >\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={scrollToTop}\n              className=\"hover:bg-primary hover:text-primary-foreground transition-all duration-300\"\n            >\n              <ArrowUp className=\"h-4 w-4 mr-2\" />\n              Back to Top\n            </Button>\n          </motion.div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDACX,qHAAA,CAAA,eAAY,CAAC,IAAI;;;;;;kDAEpB,8OAAC;wCAAE,WAAU;kDACV,qHAAA,CAAA,eAAY,CAAC,GAAG;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDACZ,qHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,KAAK,GAAG;gDACd,QAAO;gDACP,KAAI;gDACJ,YAAY;oDAAE,OAAO;oDAAK,GAAG,CAAC;gDAAE;gDAChC,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAW,KAAK,IAAI;;;;;;kEAEpC,8OAAC;wDAAI,WAAU;;;;;;;+CAVV,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;sCAkBxB,8OAAC;sCACC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAI,WAAU;kDACZ,qHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,qBACpB,8OAAC;gDAEC,MAAM,KAAK,IAAI;gDACf,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,gBAAgB,KAAK,IAAI;gDAC3B;gDACA,WAAU;0DAET,KAAK,IAAI;+CARL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;sCAgBxB,8OAAC;sCACC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAG,qHAAA,CAAA,eAAY,CAAC,KAAK;;;;;;0DACtB,8OAAC;0DAAG,qHAAA,CAAA,eAAY,CAAC,KAAK;;;;;;0DACtB,8OAAC;0DAAG,qHAAA,CAAA,eAAY,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMjC,8OAAC,qIAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;8BAGrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;4BAAE;4BACtB,aAAa;gCAAE,SAAS;4BAAE;4BAC1B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;gCACX;gCACI,IAAI,OAAO,WAAW;gCAAG;gCAAE,qHAAA,CAAA,eAAY,CAAC,IAAI;gCAAC;gCAAY;8CAC5D,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAsC;;;;;;;sCAGzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,aAAa;gCAAE,SAAS;4BAAE;4BAC1B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion, useScroll, useTransform } from \"framer-motion\";\nimport { ArrowDown, Play, Github, Linkedin, Mail, Sparkles } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { personalInfo, socialLinks } from \"@/data/social\";\nimport { useRef } from \"react\";\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      delayChildren: 0.5,\n      staggerChildren: 0.15,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { y: 30, opacity: 0 },\n  visible: {\n    y: 0,\n    opacity: 1,\n    transition: {\n      duration: 0.8,\n      ease: [0.25, 0.25, 0.25, 0.75],\n    },\n  },\n};\n\nconst floatingVariants = {\n  animate: {\n    y: [0, -20, 0],\n    rotate: [0, 5, -5, 0],\n    transition: {\n      duration: 6,\n      repeat: Infinity,\n      ease: \"easeInOut\",\n    },\n  },\n};\n\nexport function Hero() {\n  const ref = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start start\", \"end start\"],\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], [\"0%\", \"50%\"]);\n  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: \"smooth\" });\n    }\n  };\n\n  return (\n    <motion.section\n      ref={ref}\n      id=\"home\"\n      className=\"min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-background via-background to-accent/5 pt-16\"\n      style={{ y, opacity }}\n    >\n      {/* Futuristic Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        {/* Animated grid background */}\n        <div className=\"absolute inset-0 bg-grid opacity-30\" />\n        \n        {/* Floating orbs */}\n        <motion.div \n          variants={floatingVariants}\n          animate=\"animate\"\n          className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-teal-400/20 to-blue-500/20 rounded-full blur-3xl\"\n        />\n        <motion.div \n          variants={floatingVariants}\n          animate=\"animate\"\n          transition={{ delay: 2 }}\n          className=\"absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl\"\n        />\n        <motion.div \n          variants={floatingVariants}\n          animate=\"animate\"\n          transition={{ delay: 4 }}\n          className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-teal-500/10 to-blue-600/10 rounded-full blur-3xl\"\n        />\n        \n        {/* Animated Moving Circles */}\n        <motion.div\n          className=\"absolute w-16 h-16 bg-teal-400/30 rounded-full blur-sm\"\n          animate={{\n            x: [0, 100, 200, 100, 0],\n            y: [0, 150, 50, 200, 0],\n            scale: [1, 1.2, 0.8, 1.5, 1],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n          style={{ top: \"20%\", left: \"10%\" }}\n        />\n\n        <motion.div\n          className=\"absolute w-12 h-12 bg-blue-500/40 rounded-full blur-sm\"\n          animate={{\n            x: [0, -80, -160, -80, 0],\n            y: [0, 100, -50, 120, 0],\n            scale: [1, 0.7, 1.3, 0.9, 1],\n          }}\n          transition={{\n            duration: 10,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 1,\n          }}\n          style={{ top: \"60%\", right: \"15%\" }}\n        />\n\n        <motion.div\n          className=\"absolute w-20 h-20 bg-purple-500/25 rounded-full blur-sm\"\n          animate={{\n            x: [0, 120, -60, 80, 0],\n            y: [0, -80, 100, -40, 0],\n            scale: [1, 1.4, 0.6, 1.1, 1],\n          }}\n          transition={{\n            duration: 12,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 2,\n          }}\n          style={{ top: \"40%\", left: \"70%\" }}\n        />\n\n        <motion.div\n          className=\"absolute w-8 h-8 bg-cyan-400/50 rounded-full blur-sm\"\n          animate={{\n            x: [0, -50, 100, -30, 0],\n            y: [0, 80, -100, 60, 0],\n            scale: [1, 1.6, 0.4, 1.2, 1],\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 3,\n          }}\n          style={{ top: \"80%\", left: \"20%\" }}\n        />\n\n        <motion.div\n          className=\"absolute w-14 h-14 bg-pink-400/35 rounded-full blur-sm\"\n          animate={{\n            x: [0, 90, -120, 60, 0],\n            y: [0, -120, 80, -60, 0],\n            scale: [1, 0.8, 1.5, 0.7, 1],\n          }}\n          transition={{\n            duration: 9,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 4,\n          }}\n          style={{ top: \"10%\", right: \"25%\" }}\n        />\n\n        <motion.div\n          className=\"absolute w-10 h-10 bg-emerald-400/45 rounded-full blur-sm\"\n          animate={{\n            x: [0, -70, 140, -90, 0],\n            y: [0, 110, -80, 90, 0],\n            scale: [1, 1.3, 0.5, 1.4, 1],\n          }}\n          transition={{\n            duration: 11,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 5,\n          }}\n          style={{ top: \"70%\", right: \"30%\" }}\n        />\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"max-w-7xl mx-auto\"\n        >\n          {/* Main Content Grid - Profile Left, Content Right */}\n          <div className=\"grid lg:grid-cols-[400px_1fr] gap-3 lg:gap-4 items-start lg:items-center min-h-[calc(100vh-4rem)] pt-8 lg:pt-0\">\n            {/* Left Side - Profile Image */}\n            <motion.div variants={itemVariants} className=\"flex justify-center lg:justify-start order-2 lg:order-1 lg:-mt-12\">\n              <div className=\"relative\">\n                <motion.div\n                  className=\"w-72 h-72 lg:w-80 lg:h-80 rounded-full bg-gradient-to-r from-teal-400 via-blue-500 to-purple-600 p-2 shadow-2xl shadow-teal-500/25\"\n                  whileHover={{ scale: 1.05 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  <div className=\"w-full h-full rounded-full bg-background relative overflow-hidden\">\n                    {/* Profile Image */}\n                    <img\n                      src={personalInfo.avatar}\n                      alt={personalInfo.name}\n                      className=\"w-full h-full object-cover rounded-full\"\n                      onError={(e) => {\n                        // Fallback to initials if image fails to load\n                        const target = e.target as HTMLImageElement;\n                        target.style.display = 'none';\n                        const fallback = target.nextElementSibling as HTMLElement;\n                        if (fallback) fallback.style.display = 'flex';\n                      }}\n                    />\n\n                    {/* Fallback Initials */}\n                    <div className=\"absolute inset-0 flex items-center justify-center text-6xl lg:text-7xl font-bold gradient-text\" style={{ display: 'none' }}>\n                      <motion.div\n                        className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent\"\n                        animate={{ x: ['-100%', '100%'] }}\n                        transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}\n                      />\n                      {personalInfo.name.split(' ').map(n => n[0]).join('')}\n                    </div>\n\n                    {/* Overlay Animation */}\n                    <motion.div\n                      className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-full\"\n                      animate={{ x: ['-100%', '100%'] }}\n                      transition={{ duration: 3, repeat: Infinity, repeatDelay: 4 }}\n                    />\n                  </div>\n                </motion.div>\n                \n                {/* Status Indicator */}\n                <motion.div \n                  className=\"absolute -bottom-4 -right-4 w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-4 border-background flex items-center justify-center\"\n                  animate={{ scale: [1, 1.1, 1] }}\n                  transition={{ duration: 2, repeat: Infinity }}\n                >\n                  <Sparkles className=\"w-8 h-8 text-white\" />\n                </motion.div>\n\n                {/* Floating Elements around Profile */}\n                <motion.div\n                  className=\"absolute -top-8 -left-8 w-6 h-6 bg-teal-400 rounded-full\"\n                  animate={{ y: [0, -10, 0], rotate: [0, 180, 360] }}\n                  transition={{ duration: 4, repeat: Infinity }}\n                />\n                <motion.div\n                  className=\"absolute top-16 -right-12 w-4 h-4 bg-blue-500 rounded-full\"\n                  animate={{ y: [0, 15, 0], rotate: [0, -180, -360] }}\n                  transition={{ duration: 5, repeat: Infinity, delay: 1 }}\n                />\n                <motion.div\n                  className=\"absolute -bottom-12 -left-12 w-8 h-8 bg-purple-600 rounded-full\"\n                  animate={{ y: [0, -20, 0], rotate: [0, 90, 180] }}\n                  transition={{ duration: 6, repeat: Infinity, delay: 2 }}\n                />\n              </div>\n            </motion.div>\n\n            {/* Right Side - Content */}\n            <motion.div variants={itemVariants} className=\"text-center lg:text-left order-1 lg:order-2 bg-white/5 backdrop-blur-sm rounded-3xl p-4 lg:p-6 border border-white/10 w-full min-h-[500px] lg:min-h-[600px] flex flex-col justify-center\">\n              {/* Main Heading */}\n              <motion.h1\n                className=\"text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold mb-4 lg:mb-6 leading-tight\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 1, delay: 1 }}\n              >\n                <motion.span\n                  className=\"bg-gradient-to-r from-cyan-400 via-teal-400 to-blue-500 bg-clip-text text-transparent block font-extrabold drop-shadow-2xl\"\n                  initial={{ x: 0, opacity: 0 }}\n                  animate={{ x: 0, opacity: 1 }}\n                  transition={{ duration: 0.8, delay: 1.2 }}\n                  style={{\n                    filter: 'drop-shadow(0 0 20px rgba(34, 211, 238, 0.3))',\n                    wordBreak: 'keep-all',\n                    whiteSpace: 'nowrap',\n                    overflow: 'visible'\n                  }}\n                >\n                  {personalInfo.name}\n                </motion.span>\n              </motion.h1>\n\n              {/* Animated Subtitle */}\n              <motion.h2\n                className=\"text-lg sm:text-xl lg:text-2xl mb-4 lg:mb-6 font-semibold\"\n                initial={{ y: 20, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ duration: 0.8, delay: 1.6 }}\n                style={{\n                  background: 'linear-gradient(135deg, #60a5fa, #34d399, #fbbf24)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                  filter: 'drop-shadow(0 0 10px rgba(96, 165, 250, 0.2))'\n                }}\n              >\n                {personalInfo.title}\n              </motion.h2>\n\n              {/* Description */}\n              <motion.p\n                className=\"text-base lg:text-lg mb-6 leading-relaxed text-slate-200 font-medium\"\n                initial={{ y: 20, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ duration: 0.8, delay: 1.8 }}\n                style={{\n                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'\n                }}\n              >\n                {personalInfo.subtitle}\n              </motion.p>\n\n              {/* Bio */}\n              <motion.p\n                className=\"text-sm lg:text-base mb-6 lg:mb-8 leading-relaxed text-slate-300 font-normal\"\n                initial={{ y: 20, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ duration: 0.8, delay: 2 }}\n                style={{\n                  textShadow: '0 1px 3px rgba(0, 0, 0, 0.4)'\n                }}\n              >\n                {personalInfo.bio}\n              </motion.p>\n\n              {/* Futuristic CTA Buttons */}\n              <motion.div\n                className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-6 lg:mb-8\"\n                initial={{ y: 20, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ duration: 0.8, delay: 2.2 }}\n              >\n                <motion.button\n                  className=\"px-8 py-4 bg-gradient-to-r from-cyan-500 via-teal-500 to-blue-600 text-white rounded-2xl font-semibold text-lg shadow-lg hover:shadow-xl hover:shadow-cyan-500/30 transition-all duration-300 group relative overflow-hidden\"\n                  onClick={() => scrollToSection(\"#projects\")}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  style={{\n                    boxShadow: '0 8px 32px rgba(34, 211, 238, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2)'\n                  }}\n                >\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                  <Play className=\"mr-3 h-6 w-6 group-hover:rotate-12 transition-transform duration-300 relative z-10\" />\n                  <span className=\"text-lg font-semibold relative z-10\">View My Work</span>\n                </motion.button>\n\n                <motion.button\n                  className=\"px-8 py-4 bg-transparent border-2 border-cyan-400 text-cyan-400 rounded-2xl font-bold hover:bg-gradient-to-r hover:from-cyan-400 hover:to-teal-400 hover:text-black transition-all duration-300 group relative overflow-hidden\"\n                  onClick={() => scrollToSection(\"#contact\")}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  style={{\n                    boxShadow: '0 4px 20px rgba(34, 211, 238, 0.15), inset 0 1px 0 rgba(34, 211, 238, 0.1)'\n                  }}\n                >\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                  <Mail className=\"mr-3 h-5 w-5 group-hover:rotate-12 transition-transform duration-300 relative z-10\" />\n                  <span className=\"font-bold relative z-10\">Get In Touch</span>\n                </motion.button>\n              </motion.div>\n\n              {/* Futuristic Social Links */}\n              <motion.div\n                className=\"flex justify-center lg:justify-start space-x-4 mb-4\"\n                initial={{ y: 20, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ duration: 0.8, delay: 2.4 }}\n              >\n                {socialLinks.slice(0, 3).map((link, index) => {\n                  const IconComponent = link.icon === \"Github\" ? Github :\n                                       link.icon === \"Linkedin\" ? Linkedin : Mail;\n\n                  return (\n                    <motion.a\n                      key={link.name}\n                      href={link.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"relative group\"\n                      whileHover={{ scale: 1.2, y: -5 }}\n                      whileTap={{ scale: 0.9 }}\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 2.6 + index * 0.1 }}\n                    >\n                      <div className=\"w-14 h-14 rounded-2xl bg-white/5 backdrop-blur-sm flex items-center justify-center hover:shadow-lg hover:shadow-teal-500/25 transition-all duration-300 border border-teal-500/20 group-hover:border-teal-400/60\">\n                        <IconComponent className=\"h-6 w-6 text-gray-300 group-hover:text-teal-400 transition-colors duration-300\" />\n                      </div>\n                      <span className=\"absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-gray-300 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                        {link.name}\n                      </span>\n                    </motion.a>\n                  );\n                })}\n              </motion.div>\n            </motion.div>\n          </div>\n\n          {/* Animated Scroll Indicator */}\n          <motion.div\n            className=\"flex flex-col items-center mt-16\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 3 }}\n          >\n            <motion.p\n              className=\"text-sm text-gray-300 mb-6 font-medium tracking-wider uppercase\"\n              animate={{ opacity: [0.7, 1, 0.7] }}\n              transition={{ duration: 2, repeat: Infinity }}\n            >\n              Scroll to explore\n            </motion.p>\n            <motion.button\n              onClick={() => scrollToSection(\"#about\")}\n              className=\"relative group\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              <div className=\"w-12 h-12 rounded-full bg-white/5 backdrop-blur-sm border border-teal-500/30 flex items-center justify-center group-hover:border-teal-400/60 transition-all duration-300\">\n                <motion.div\n                  animate={{ y: [0, 8, 0] }}\n                  transition={{ duration: 2, repeat: Infinity }}\n                >\n                  <ArrowDown className=\"h-5 w-5 text-teal-400\" />\n                </motion.div>\n              </div>\n              <div className=\"absolute inset-0 rounded-full bg-teal-400/20 scale-0 group-hover:scale-110 transition-transform duration-300\" />\n            </motion.button>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Floating particles */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 bg-teal-400/30 rounded-full\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n            animate={{\n              y: [0, -100, 0],\n              opacity: [0, 1, 0],\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2,\n            }}\n          />\n        ))}\n      </div>\n    </motion.section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,eAAe;YACf,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,GAAG;QAAI,SAAS;IAAE;IAC5B,SAAS;QACP,GAAG;QACH,SAAS;QACT,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAM;gBAAM;gBAAM;aAAK;QAChC;IACF;AACF;AAEA,MAAM,mBAAmB;IACvB,SAAS;QACP,GAAG;YAAC;YAAG,CAAC;YAAI;SAAE;QACd,QAAQ;YAAC;YAAG;YAAG,CAAC;YAAG;SAAE;QACrB,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;AACF;AAEO,SAAS;IACd,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAe;SAAY;IACtC;IAEA,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAM;KAAM;IAC7D,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG;KAAE;IAE9D,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,IAAG;QACH,WAAU;QACV,OAAO;YAAE;YAAG;QAAQ;;0BAGpB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,WAAU;;;;;;kCAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,YAAY;4BAAE,OAAO;wBAAE;wBACvB,WAAU;;;;;;kCAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,YAAY;4BAAE,OAAO;wBAAE;wBACvB,WAAU;;;;;;kCAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAK;gCAAK;gCAAK;6BAAE;4BACxB,GAAG;gCAAC;gCAAG;gCAAK;gCAAI;gCAAK;6BAAE;4BACvB,OAAO;gCAAC;gCAAG;gCAAK;gCAAK;gCAAK;6BAAE;wBAC9B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,OAAO;4BAAE,KAAK;4BAAO,MAAM;wBAAM;;;;;;kCAGnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI,CAAC;gCAAK,CAAC;gCAAI;6BAAE;4BACzB,GAAG;gCAAC;gCAAG;gCAAK,CAAC;gCAAI;gCAAK;6BAAE;4BACxB,OAAO;gCAAC;gCAAG;gCAAK;gCAAK;gCAAK;6BAAE;wBAC9B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;wBACA,OAAO;4BAAE,KAAK;4BAAO,OAAO;wBAAM;;;;;;kCAGpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAK,CAAC;gCAAI;gCAAI;6BAAE;4BACvB,GAAG;gCAAC;gCAAG,CAAC;gCAAI;gCAAK,CAAC;gCAAI;6BAAE;4BACxB,OAAO;gCAAC;gCAAG;gCAAK;gCAAK;gCAAK;6BAAE;wBAC9B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;wBACA,OAAO;4BAAE,KAAK;4BAAO,MAAM;wBAAM;;;;;;kCAGnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;gCAAK,CAAC;gCAAI;6BAAE;4BACxB,GAAG;gCAAC;gCAAG;gCAAI,CAAC;gCAAK;gCAAI;6BAAE;4BACvB,OAAO;gCAAC;gCAAG;gCAAK;gCAAK;gCAAK;6BAAE;wBAC9B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;wBACA,OAAO;4BAAE,KAAK;4BAAO,MAAM;wBAAM;;;;;;kCAGnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAI,CAAC;gCAAK;gCAAI;6BAAE;4BACvB,GAAG;gCAAC;gCAAG,CAAC;gCAAK;gCAAI,CAAC;gCAAI;6BAAE;4BACxB,OAAO;gCAAC;gCAAG;gCAAK;gCAAK;gCAAK;6BAAE;wBAC9B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;wBACA,OAAO;4BAAE,KAAK;4BAAO,OAAO;wBAAM;;;;;;kCAGpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;gCAAK,CAAC;gCAAI;6BAAE;4BACxB,GAAG;gCAAC;gCAAG;gCAAK,CAAC;gCAAI;gCAAI;6BAAE;4BACvB,OAAO;gCAAC;gCAAG;gCAAK;gCAAK;gCAAK;6BAAE;wBAC9B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;wBACA,OAAO;4BAAE,KAAK;4BAAO,OAAO;wBAAM;;;;;;;;;;;;0BAItC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;8CAC5C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DACC,KAAK,qHAAA,CAAA,eAAY,CAAC,MAAM;4DACxB,KAAK,qHAAA,CAAA,eAAY,CAAC,IAAI;4DACtB,WAAU;4DACV,SAAS,CAAC;gEACR,8CAA8C;gEAC9C,MAAM,SAAS,EAAE,MAAM;gEACvB,OAAO,KAAK,CAAC,OAAO,GAAG;gEACvB,MAAM,WAAW,OAAO,kBAAkB;gEAC1C,IAAI,UAAU,SAAS,KAAK,CAAC,OAAO,GAAG;4DACzC;;;;;;sEAIF,8OAAC;4DAAI,WAAU;4DAAiG,OAAO;gEAAE,SAAS;4DAAO;;8EACvI,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEAAE,GAAG;4EAAC;4EAAS;yEAAO;oEAAC;oEAChC,YAAY;wEAAE,UAAU;wEAAG,QAAQ;wEAAU,aAAa;oEAAE;;;;;;gEAE7D,qHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;sEAIpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,GAAG;oEAAC;oEAAS;iEAAO;4DAAC;4DAChC,YAAY;gEAAE,UAAU;gEAAG,QAAQ;gEAAU,aAAa;4DAAE;;;;;;;;;;;;;;;;;0DAMlE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;gDAAC;gDAC9B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;0DAE5C,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAItB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,GAAG;wDAAC;wDAAG,CAAC;wDAAI;qDAAE;oDAAE,QAAQ;wDAAC;wDAAG;wDAAK;qDAAI;gDAAC;gDACjD,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;;;;;;0DAE9C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,GAAG;wDAAC;wDAAG;wDAAI;qDAAE;oDAAE,QAAQ;wDAAC;wDAAG,CAAC;wDAAK,CAAC;qDAAI;gDAAC;gDAClD,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,OAAO;gDAAE;;;;;;0DAExD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,GAAG;wDAAC;wDAAG,CAAC;wDAAI;qDAAE;oDAAE,QAAQ;wDAAC;wDAAG;wDAAI;qDAAI;gDAAC;gDAChD,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,OAAO;gDAAE;;;;;;;;;;;;;;;;;8CAM5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAE5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CACV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,SAAS;4CAAE;4CACtB,YAAY;gDAAE,UAAU;gDAAG,OAAO;4CAAE;sDAEpC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,WAAU;gDACV,SAAS;oDAAE,GAAG;oDAAG,SAAS;gDAAE;gDAC5B,SAAS;oDAAE,GAAG;oDAAG,SAAS;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,OAAO;oDACL,QAAQ;oDACR,WAAW;oDACX,YAAY;oDACZ,UAAU;gDACZ;0DAEC,qHAAA,CAAA,eAAY,CAAC,IAAI;;;;;;;;;;;sDAKtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CACV,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,OAAO;gDACL,YAAY;gDACZ,sBAAsB;gDACtB,qBAAqB;gDACrB,gBAAgB;gDAChB,QAAQ;4CACV;sDAEC,qHAAA,CAAA,eAAY,CAAC,KAAK;;;;;;sDAIrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,WAAU;4CACV,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,OAAO;gDACL,YAAY;4CACd;sDAEC,qHAAA,CAAA,eAAY,CAAC,QAAQ;;;;;;sDAIxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,WAAU;4CACV,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAE;4CACtC,OAAO;gDACL,YAAY;4CACd;sDAEC,qHAAA,CAAA,eAAY,CAAC,GAAG;;;;;;sDAInB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;;8DAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,WAAU;oDACV,SAAS,IAAM,gBAAgB;oDAC/B,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;oDACxB,OAAO;wDACL,WAAW;oDACb;;sEAEA,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAsC;;;;;;;;;;;;8DAGxD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,WAAU;oDACV,SAAS,IAAM,gBAAgB;oDAC/B,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;oDACxB,OAAO;wDACL,WAAW;oDACb;;sEAEA,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;sDAK9C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;sDAEvC,qHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM;gDAClC,MAAM,gBAAgB,KAAK,IAAI,KAAK,WAAW,sMAAA,CAAA,SAAM,GAChC,KAAK,IAAI,KAAK,aAAa,0MAAA,CAAA,WAAQ,GAAG,kMAAA,CAAA,OAAI;gDAE/D,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,KAAK,GAAG;oDACd,QAAO;oDACP,KAAI;oDACJ,WAAU;oDACV,YAAY;wDAAE,OAAO;wDAAK,GAAG,CAAC;oDAAE;oDAChC,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO,MAAM,QAAQ;oDAAI;;sEAEvC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAc,WAAU;;;;;;;;;;;sEAE3B,8OAAC;4DAAK,WAAU;sEACb,KAAK,IAAI;;;;;;;mDAfP,KAAK,IAAI;;;;;4CAmBpB;;;;;;;;;;;;;;;;;;sCAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAE;;8CAEvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCAAC;oCAClC,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;8CAC7C;;;;;;8CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;;sDAEvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC;wDAAG;wDAAG;qDAAE;gDAAC;gDACxB,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;0DAE5C,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGzB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wBAChC;wBACA,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAK;6BAAE;4BACf,SAAS;gCAAC;gCAAG;gCAAG;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU,IAAI,KAAK,MAAM,KAAK;4BAC9B,QAAQ;4BACR,OAAO,KAAK,MAAM,KAAK;wBACzB;uBAdK;;;;;;;;;;;;;;;;AAoBjB", "debugId": null}}, {"offset": {"line": 1856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/about.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { MapPin, Mail, Phone, Calendar, Heart, Coffee } from \"lucide-react\";\nimport { personalInfo } from \"@/data/social\";\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      delayChildren: 0.3,\n      staggerChildren: 0.2,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { y: 20, opacity: 0 },\n  visible: {\n    y: 0,\n    opacity: 1,\n    transition: {\n      duration: 0.5,\n    },\n  },\n};\n\nexport function About() {\n  return (\n    <section id=\"about\" className=\"py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-4xl lg:text-5xl font-bold mb-6\">\n              <span className=\"bg-gradient-to-r from-cyan-400 via-teal-400 to-blue-500 bg-clip-text text-transparent\">\n                About Me\n              </span>\n            </h2>\n            <p className=\"text-lg text-slate-300 max-w-2xl mx-auto\">\n              Get to know the person behind the code\n            </p>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\n            {/* Personal Story */}\n            <motion.div variants={itemVariants}>\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all duration-300\">\n                <h3 className=\"text-2xl font-bold mb-6 text-white\">My Story</h3>\n                <div className=\"space-y-4 text-slate-300 leading-relaxed\">\n                  <p>\n                    {personalInfo.bio}\n                  </p>\n                  <p>\n                    I believe in continuous learning and staying updated with the latest\n                    technologies. My approach combines technical expertise with creative\n                    problem-solving to deliver solutions that not only work well but also\n                    provide exceptional user experiences.\n                  </p>\n                  <p>\n                    When I'm not coding, you can find me creating content for my YouTube channel,\n                    working out at the gym, or exploring new places. I'm passionate about\n                    sharing knowledge and helping others grow in their development journey.\n                  </p>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"grid grid-cols-2 gap-4 mt-8\">\n                  <div className=\"text-center p-4 rounded-lg bg-white/10 border border-white/20\">\n                    <div className=\"text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent\">3+</div>\n                    <div className=\"text-sm text-slate-400\">Years Experience</div>\n                  </div>\n                  <div className=\"text-center p-4 rounded-lg bg-white/10 border border-white/20\">\n                    <div className=\"text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent\">50+</div>\n                    <div className=\"text-sm text-slate-400\">Projects Completed</div>\n                  </div>\n                  <div className=\"text-center p-4 rounded-lg bg-white/10 border border-white/20\">\n                    <div className=\"text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent\">100+</div>\n                    <div className=\"text-sm text-slate-400\">Happy Clients</div>\n                  </div>\n                  <div className=\"text-center p-4 rounded-lg bg-white/10 border border-white/20\">\n                    <div className=\"text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent\">24/7</div>\n                    <div className=\"text-sm text-slate-400\">Support</div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Personal Information */}\n            <motion.div variants={itemVariants}>\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all duration-300\">\n                <h3 className=\"text-2xl font-bold mb-6 text-white\">Personal Details</h3>\n\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"p-3 rounded-xl bg-gradient-to-r from-cyan-400 to-blue-500\">\n                      <MapPin className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"text-slate-400 text-sm\">Location</p>\n                      <p className=\"text-white font-medium\">{personalInfo.location}</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"p-3 rounded-xl bg-gradient-to-r from-green-400 to-emerald-500\">\n                      <Mail className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"text-slate-400 text-sm\">Email</p>\n                      <p className=\"text-white font-medium\">{personalInfo.email}</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"p-3 rounded-xl bg-gradient-to-r from-purple-400 to-pink-500\">\n                      <Phone className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"text-slate-400 text-sm\">Phone</p>\n                      <p className=\"text-white font-medium\">{personalInfo.phone}</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"p-3 rounded-xl bg-gradient-to-r from-orange-400 to-red-500\">\n                      <Heart className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"text-slate-400 text-sm\">Interests</p>\n                      <p className=\"text-white font-medium\">Technology, Fitness, Content Creation</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"p-3 rounded-xl bg-gradient-to-r from-teal-400 to-cyan-500\">\n                      <Coffee className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <div>\n                      <p className=\"text-slate-400 text-sm\">Fun Fact</p>\n                      <p className=\"text-white font-medium\">I can code for hours with just coffee ☕</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Personality Traits */}\n                <div className=\"mt-8\">\n                  <h4 className=\"text-lg font-semibold mb-4 text-white\">What Drives Me</h4>\n                  <div className=\"grid grid-cols-2 gap-3\">\n                    <div className=\"p-3 rounded-lg bg-white/10 border border-white/20 text-center\">\n                      <p className=\"text-cyan-400 font-semibold\">Problem Solver</p>\n                    </div>\n                    <div className=\"p-3 rounded-lg bg-white/10 border border-white/20 text-center\">\n                      <p className=\"text-green-400 font-semibold\">Team Player</p>\n                    </div>\n                    <div className=\"p-3 rounded-lg bg-white/10 border border-white/20 text-center\">\n                      <p className=\"text-purple-400 font-semibold\">Creative Thinker</p>\n                    </div>\n                    <div className=\"p-3 rounded-lg bg-white/10 border border-white/20 text-center\">\n                      <p className=\"text-orange-400 font-semibold\">Lifelong Learner</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;AAOA,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,eAAe;YACf,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,GAAG;QAAI,SAAS;IAAE;IAC5B,SAAS;QACP,GAAG;QACH,SAAS;QACT,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAGpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAwF;;;;;;;;;;;0CAI1G,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAK1D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DACE,qHAAA,CAAA,eAAY,CAAC,GAAG;;;;;;8DAEnB,8OAAC;8DAAE;;;;;;8DAMH,8OAAC;8DAAE;;;;;;;;;;;;sDAQL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA8F;;;;;;sEAC7G,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;8DAE1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA8F;;;;;;sEAC7G,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;8DAE1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA8F;;;;;;sEAC7G,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;8DAE1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA8F;;;;;;sEAC7G,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAEnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;sEAEpB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;8EACtC,8OAAC;oEAAE,WAAU;8EAA0B,qHAAA,CAAA,eAAY,CAAC,QAAQ;;;;;;;;;;;;;;;;;;8DAIhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;8EACtC,8OAAC;oEAAE,WAAU;8EAA0B,qHAAA,CAAA,eAAY,CAAC,KAAK;;;;;;;;;;;;;;;;;;8DAI7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;8EACtC,8OAAC;oEAAE,WAAU;8EAA0B,qHAAA,CAAA,eAAY,CAAC,KAAK;;;;;;;;;;;;;;;;;;8DAI7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;8EACtC,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;8DAI1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;sEAEpB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;8EACtC,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;;;;;;;sDAM5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;sEAE7C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EAA+B;;;;;;;;;;;sEAE9C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;sEAE/C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnE", "debugId": null}}, {"offset": {"line": 2494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/skills.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Code, Database, Globe, Smartphone, Palette, Server } from \"lucide-react\";\nimport { useState } from \"react\";\n\nconst skillCategories = [\n  {\n    title: \"Frontend Development\",\n    icon: Globe,\n    skills: [\n      { name: \"React.js\", level: 95, logo: \"⚛️\" },\n      { name: \"Next.js\", level: 90, logo: \"▲\" },\n      { name: \"TypeScript\", level: 88, logo: \"🔷\" },\n      { name: \"JavaScript\", level: 95, logo: \"🟨\" },\n      { name: \"HTML5\", level: 98, logo: \"🌐\" },\n      { name: \"CSS3\", level: 95, logo: \"🎨\" },\n      { name: \"Tailwind CSS\", level: 92, logo: \"💨\" },\n      { name: \"Framer Motion\", level: 85, logo: \"🎭\" }\n    ],\n    color: \"from-blue-400 to-cyan-400\"\n  },\n  {\n    title: \"Backend Development\",\n    icon: Server,\n    skills: [\n      { name: \"Node.js\", level: 88, logo: \"🟢\" },\n      { name: \"Express.js\", level: 85, logo: \"🚀\" },\n      { name: \"Python\", level: 80, logo: \"🐍\" },\n      { name: \"PHP\", level: 75, logo: \"🐘\" },\n      { name: \"REST APIs\", level: 90, logo: \"🔗\" },\n      { name: \"GraphQL\", level: 70, logo: \"📊\" }\n    ],\n    color: \"from-green-400 to-emerald-400\"\n  },\n  {\n    title: \"Database & Tools\",\n    icon: Database,\n    skills: [\n      { name: \"MongoDB\", level: 85, logo: \"🍃\" },\n      { name: \"MySQL\", level: 88, logo: \"🐬\" },\n      { name: \"PostgreSQL\", level: 80, logo: \"🐘\" },\n      { name: \"Firebase\", level: 85, logo: \"🔥\" },\n      { name: \"Git\", level: 92, logo: \"📝\" },\n      { name: \"Docker\", level: 75, logo: \"🐳\" }\n    ],\n    color: \"from-purple-400 to-pink-400\"\n  },\n  {\n    title: \"UI/UX Design\",\n    icon: Palette,\n    skills: [\n      { name: \"Figma\", level: 90, logo: \"🎨\" },\n      { name: \"Adobe XD\", level: 85, logo: \"💎\" },\n      { name: \"Photoshop\", level: 80, logo: \"🖼️\" },\n      { name: \"Illustrator\", level: 75, logo: \"✏️\" },\n      { name: \"User Research\", level: 85, logo: \"🔍\" },\n      { name: \"Prototyping\", level: 88, logo: \"🛠️\" }\n    ],\n    color: \"from-orange-400 to-red-400\"\n  }\n];\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.2\n    }\n  }\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 20 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6\n    }\n  }\n};\n\nexport function Skills() {\n  const [flippedCards, setFlippedCards] = useState<Set<string>>(new Set());\n\n  const toggleCard = (skillId: string) => {\n    setFlippedCards(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(skillId)) {\n        newSet.delete(skillId);\n      } else {\n        newSet.add(skillId);\n      }\n      return newSet;\n    });\n  };\n\n  return (\n    <section id=\"skills\" className=\"py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl lg:text-5xl font-bold mb-6\"\n          >\n            <span className=\"bg-gradient-to-r from-cyan-400 via-teal-400 to-blue-500 bg-clip-text text-transparent\">\n              Skills & Expertise\n            </span>\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-lg text-slate-300 max-w-2xl mx-auto\"\n          >\n            Technologies and tools I use to bring ideas to life. Click cards to see progress details.\n          </motion.p>\n        </motion.div>\n\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 gap-8\"\n        >\n          {skillCategories.map((category, categoryIndex) => (\n            <motion.div\n              key={category.title}\n              variants={itemVariants}\n              className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300\"\n            >\n              <div className=\"flex items-center mb-6\">\n                <div className={`p-3 rounded-xl bg-gradient-to-r ${category.color} mr-4`}>\n                  <category.icon className=\"w-6 h-6 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-bold text-white\">{category.title}</h3>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-3\">\n                {category.skills.map((skill, skillIndex) => {\n                  const skillId = `${categoryIndex}-${skillIndex}`;\n                  const isFlipped = flippedCards.has(skillId);\n\n                  return (\n                    <motion.div\n                      key={skill.name}\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      whileInView={{ opacity: 1, scale: 1 }}\n                      viewport={{ once: true }}\n                      transition={{ duration: 0.5, delay: categoryIndex * 0.1 + skillIndex * 0.05 }}\n                      whileHover={{ scale: 1.05 }}\n                      onClick={() => toggleCard(skillId)}\n                      className=\"relative h-24 cursor-pointer\"\n                      style={{ perspective: \"1000px\" }}\n                    >\n                      <motion.div\n                        className=\"relative w-full h-full\"\n                        animate={{ rotateY: isFlipped ? 180 : 0 }}\n                        transition={{ duration: 0.6 }}\n                        style={{ transformStyle: \"preserve-3d\" }}\n                      >\n                        {/* Front Side */}\n                        <div\n                          className=\"absolute inset-0 bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 hover:border-white/30 transition-all duration-300 group\"\n                          style={{ backfaceVisibility: \"hidden\" }}\n                        >\n                          <div className=\"text-center h-full flex flex-col justify-center\">\n                            <div className=\"text-2xl mb-2\">{skill.logo}</div>\n                            <h4 className=\"text-white font-semibold text-sm mb-1 group-hover:text-cyan-400 transition-colors\">\n                              {skill.name}\n                            </h4>\n                            <div className=\"flex justify-center\">\n                              {[...Array(5)].map((_, i) => (\n                                <div\n                                  key={i}\n                                  className={`w-1.5 h-1.5 rounded-full mx-0.5 ${\n                                    i < Math.ceil(skill.level / 20)\n                                      ? `bg-gradient-to-r ${category.color}`\n                                      : 'bg-slate-600'\n                                  }`}\n                                />\n                              ))}\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Back Side */}\n                        <div\n                          className=\"absolute inset-0 bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 transition-all duration-300\"\n                          style={{\n                            backfaceVisibility: \"hidden\",\n                            transform: \"rotateY(180deg)\"\n                          }}\n                        >\n                          <div className=\"h-full flex flex-col justify-center\">\n                            <div className=\"text-center mb-2\">\n                              <div className={`w-8 h-8 mx-auto mb-2 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center`}>\n                                <span className=\"text-white font-bold text-xs\">{skill.level}%</span>\n                              </div>\n                              <h4 className=\"text-white font-semibold text-xs mb-2\">{skill.name}</h4>\n                            </div>\n                            <div className=\"w-full bg-slate-700 rounded-full h-2 mb-1\">\n                              <motion.div\n                                className={`h-2 rounded-full bg-gradient-to-r ${category.color}`}\n                                initial={{ width: 0 }}\n                                animate={{ width: isFlipped ? `${skill.level}%` : 0 }}\n                                transition={{ duration: 1, delay: 0.3 }}\n                              />\n                            </div>\n                            <div className=\"text-center\">\n                              <span className=\"text-slate-400 text-xs\">Proficiency Level</span>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,QAAQ;YACN;gBAAE,MAAM;gBAAY,OAAO;gBAAI,MAAM;YAAK;YAC1C;gBAAE,MAAM;gBAAW,OAAO;gBAAI,MAAM;YAAI;YACxC;gBAAE,MAAM;gBAAc,OAAO;gBAAI,MAAM;YAAK;YAC5C;gBAAE,MAAM;gBAAc,OAAO;gBAAI,MAAM;YAAK;YAC5C;gBAAE,MAAM;gBAAS,OAAO;gBAAI,MAAM;YAAK;YACvC;gBAAE,MAAM;gBAAQ,OAAO;gBAAI,MAAM;YAAK;YACtC;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,MAAM;YAAK;YAC9C;gBAAE,MAAM;gBAAiB,OAAO;gBAAI,MAAM;YAAK;SAChD;QACD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM,sMAAA,CAAA,SAAM;QACZ,QAAQ;YACN;gBAAE,MAAM;gBAAW,OAAO;gBAAI,MAAM;YAAK;YACzC;gBAAE,MAAM;gBAAc,OAAO;gBAAI,MAAM;YAAK;YAC5C;gBAAE,MAAM;gBAAU,OAAO;gBAAI,MAAM;YAAK;YACxC;gBAAE,MAAM;gBAAO,OAAO;gBAAI,MAAM;YAAK;YACrC;gBAAE,MAAM;gBAAa,OAAO;gBAAI,MAAM;YAAK;YAC3C;gBAAE,MAAM;gBAAW,OAAO;gBAAI,MAAM;YAAK;SAC1C;QACD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM,0MAAA,CAAA,WAAQ;QACd,QAAQ;YACN;gBAAE,MAAM;gBAAW,OAAO;gBAAI,MAAM;YAAK;YACzC;gBAAE,MAAM;gBAAS,OAAO;gBAAI,MAAM;YAAK;YACvC;gBAAE,MAAM;gBAAc,OAAO;gBAAI,MAAM;YAAK;YAC5C;gBAAE,MAAM;gBAAY,OAAO;gBAAI,MAAM;YAAK;YAC1C;gBAAE,MAAM;gBAAO,OAAO;gBAAI,MAAM;YAAK;YACrC;gBAAE,MAAM;gBAAU,OAAO;gBAAI,MAAM;YAAK;SACzC;QACD,OAAO;IACT;IACA;QACE,OAAO;QACP,MAAM,wMAAA,CAAA,UAAO;QACb,QAAQ;YACN;gBAAE,MAAM;gBAAS,OAAO;gBAAI,MAAM;YAAK;YACvC;gBAAE,MAAM;gBAAY,OAAO;gBAAI,MAAM;YAAK;YAC1C;gBAAE,MAAM;gBAAa,OAAO;gBAAI,MAAM;YAAM;YAC5C;gBAAE,MAAM;gBAAe,OAAO;gBAAI,MAAM;YAAK;YAC7C;gBAAE,MAAM;gBAAiB,OAAO;gBAAI,MAAM;YAAK;YAC/C;gBAAE,MAAM;gBAAe,OAAO;gBAAI,MAAM;YAAM;SAC/C;QACD,OAAO;IACT;CACD;AAED,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAElE,MAAM,aAAa,CAAC;QAClB,gBAAgB,CAAA;YACd,MAAM,SAAS,IAAI,IAAI;YACvB,IAAI,OAAO,GAAG,CAAC,UAAU;gBACvB,OAAO,MAAM,CAAC;YAChB,OAAO;gBACL,OAAO,GAAG,CAAC;YACb;YACA,OAAO;QACT;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAK,WAAU;0CAAwF;;;;;;;;;;;sCAI1G,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,gBAAgB,GAAG,CAAC,CAAC,UAAU,8BAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,gCAAgC,EAAE,SAAS,KAAK,CAAC,KAAK,CAAC;sDACtE,cAAA,8OAAC,SAAS,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAE3B,8OAAC;4CAAG,WAAU;sDAAgC,SAAS,KAAK;;;;;;;;;;;;8CAG9D,8OAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO;wCAC3B,MAAM,UAAU,GAAG,cAAc,CAAC,EAAE,YAAY;wCAChD,MAAM,YAAY,aAAa,GAAG,CAAC;wCAEnC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;gDAAK,OAAO,gBAAgB,MAAM,aAAa;4CAAK;4CAC5E,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,SAAS,IAAM,WAAW;4CAC1B,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAS;sDAE/B,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS,YAAY,MAAM;gDAAE;gDACxC,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,OAAO;oDAAE,gBAAgB;gDAAc;;kEAGvC,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,oBAAoB;wDAAS;kEAEtC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAiB,MAAM,IAAI;;;;;;8EAC1C,8OAAC;oEAAG,WAAU;8EACX,MAAM,IAAI;;;;;;8EAEb,8OAAC;oEAAI,WAAU;8EACZ;2EAAI,MAAM;qEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4EAEC,WAAW,CAAC,gCAAgC,EAC1C,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,GAAG,MACxB,CAAC,iBAAiB,EAAE,SAAS,KAAK,EAAE,GACpC,gBACJ;2EALG;;;;;;;;;;;;;;;;;;;;;kEAaf,8OAAC;wDACC,WAAU;wDACV,OAAO;4DACL,oBAAoB;4DACpB,WAAW;wDACb;kEAEA,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAW,CAAC,iDAAiD,EAAE,SAAS,KAAK,CAAC,iCAAiC,CAAC;sFACnH,cAAA,8OAAC;gFAAK,WAAU;;oFAAgC,MAAM,KAAK;oFAAC;;;;;;;;;;;;sFAE9D,8OAAC;4EAAG,WAAU;sFAAyC,MAAM,IAAI;;;;;;;;;;;;8EAEnE,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,EAAE;wEAChE,SAAS;4EAAE,OAAO;wEAAE;wEACpB,SAAS;4EAAE,OAAO,YAAY,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG;wEAAE;wEACpD,YAAY;4EAAE,UAAU;4EAAG,OAAO;wEAAI;;;;;;;;;;;8EAG1C,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAjE5C,MAAM,IAAI;;;;;oCAwErB;;;;;;;2BA1FG,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;AAkGjC", "debugId": null}}, {"offset": {"line": 3027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3073, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/data/experience.ts"], "sourcesContent": ["import { Experience, Education } from \"@/types\";\n\nexport const experiences: Experience[] = [\n  {\n    id: \"1\",\n    company: \"TechCorp Solutions\",\n    position: \"Senior Full Stack Developer\",\n    duration: \"2022 - Present\",\n    description: [\n      \"Led development of enterprise-level web applications using React, Next.js, and Node.js\",\n      \"Architected and implemented microservices infrastructure serving 100k+ daily active users\",\n      \"Mentored junior developers and established coding standards and best practices\",\n      \"Collaborated with cross-functional teams to deliver projects 20% ahead of schedule\",\n      \"Optimized application performance resulting in 40% faster load times\"\n    ],\n    technologies: [\"React\", \"Next.js\", \"TypeScript\", \"Node.js\", \"PostgreSQL\", \"AWS\", \"Docker\"],\n    logo: \"/companies/techcorp.png\"\n  },\n  {\n    id: \"2\",\n    company: \"StartupXYZ\",\n    position: \"Full Stack Developer\",\n    duration: \"2020 - 2022\",\n    description: [\n      \"Developed and maintained multiple client projects using modern web technologies\",\n      \"Built responsive web applications with React, Vue.js, and Angular\",\n      \"Implemented RESTful APIs and GraphQL endpoints using Node.js and Python\",\n      \"Worked closely with designers to create pixel-perfect user interfaces\",\n      \"Participated in agile development processes and sprint planning\"\n    ],\n    technologies: [\"React\", \"Vue.js\", \"Angular\", \"Node.js\", \"Python\", \"MongoDB\", \"Firebase\"],\n    logo: \"/companies/startupxyz.png\"\n  },\n  {\n    id: \"3\",\n    company: \"Digital Agency Pro\",\n    position: \"Frontend Developer\",\n    duration: \"2019 - 2020\",\n    description: [\n      \"Created responsive websites and web applications for various clients\",\n      \"Collaborated with design team to implement modern UI/UX designs\",\n      \"Optimized websites for performance and SEO best practices\",\n      \"Maintained and updated existing client websites\",\n      \"Worked with CMS platforms like WordPress and Drupal\"\n    ],\n    technologies: [\"HTML5\", \"CSS3\", \"JavaScript\", \"jQuery\", \"WordPress\", \"Sass\", \"Bootstrap\"],\n    logo: \"/companies/digitalagency.png\"\n  },\n  {\n    id: \"4\",\n    company: \"FreelanceWork\",\n    position: \"Web Developer\",\n    duration: \"2018 - 2019\",\n    description: [\n      \"Provided web development services to small businesses and startups\",\n      \"Built custom websites using HTML, CSS, JavaScript, and PHP\",\n      \"Integrated third-party APIs and payment gateways\",\n      \"Managed client relationships and project timelines\",\n      \"Delivered projects on time and within budget\"\n    ],\n    technologies: [\"HTML5\", \"CSS3\", \"JavaScript\", \"PHP\", \"MySQL\", \"WordPress\"],\n    logo: \"/companies/freelance.png\"\n  }\n];\n\nexport const education: Education[] = [\n  {\n    id: \"1\",\n    institution: \"University of Technology\",\n    degree: \"Bachelor of Science\",\n    field: \"Computer Science\",\n    duration: \"2015 - 2019\",\n    description: \"Focused on software engineering, algorithms, and data structures. Graduated with honors.\",\n    gpa: \"3.8/4.0\"\n  },\n  {\n    id: \"2\",\n    institution: \"Tech Institute\",\n    degree: \"Certificate\",\n    field: \"Full Stack Web Development\",\n    duration: \"2018\",\n    description: \"Intensive bootcamp covering modern web development technologies and best practices.\"\n  },\n  {\n    id: \"3\",\n    institution: \"Online Learning Platform\",\n    degree: \"Certificate\",\n    field: \"Cloud Computing with AWS\",\n    duration: \"2021\",\n    description: \"Comprehensive course covering AWS services, cloud architecture, and DevOps practices.\"\n  }\n];\n"], "names": [], "mappings": ";;;;AAEO,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAW;YAAc;YAAW;YAAc;YAAO;SAAS;QAC1F,MAAM;IACR;IACA;QACE,IAAI;QACJ,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAU;YAAW;YAAW;YAAU;YAAW;SAAW;QACxF,MAAM;IACR;IACA;QACE,IAAI;QACJ,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAQ;YAAc;YAAU;YAAa;YAAQ;SAAY;QACzF,MAAM;IACR;IACA;QACE,IAAI;QACJ,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;YACX;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAQ;YAAc;YAAO;YAAS;SAAY;QAC1E,MAAM;IACR;CACD;AAEM,MAAM,YAAyB;IACpC;QACE,IAAI;QACJ,aAAa;QACb,QAAQ;QACR,OAAO;QACP,UAAU;QACV,aAAa;QACb,KAAK;IACP;IACA;QACE,IAAI;QACJ,aAAa;QACb,QAAQ;QACR,OAAO;QACP,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,aAAa;QACb,QAAQ;QACR,OAAO;QACP,UAAU;QACV,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 3300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/experience.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { experiences, education } from \"@/data/experience\";\nimport { Briefcase, GraduationCap, MapPin, Calendar } from \"lucide-react\";\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      delayChildren: 0.3,\n      staggerChildren: 0.2,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { y: 20, opacity: 0 },\n  visible: {\n    y: 0,\n    opacity: 1,\n    transition: {\n      duration: 0.5,\n    },\n  },\n};\n\nexport function Experience() {\n  return (\n    <section id=\"experience\" className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-4\">\n              My <span className=\"gradient-text\">Journey</span>\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              A timeline of my professional experience and educational background\n            </p>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Work Experience */}\n            <motion.div variants={itemVariants}>\n              <div className=\"flex items-center gap-3 mb-8\">\n                <div className=\"p-2 rounded-lg bg-primary/10\">\n                  <Briefcase className=\"h-6 w-6 text-primary\" />\n                </div>\n                <h3 className=\"text-2xl font-bold\">Work Experience</h3>\n              </div>\n\n              <div className=\"space-y-6\">\n                {experiences.map((exp, index) => (\n                  <motion.div\n                    key={exp.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    whileInView={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                  >\n                    <Card className=\"glass border-0 shadow-lg hover:shadow-xl transition-all duration-300\">\n                      <CardContent className=\"p-6\">\n                        <div className=\"flex items-start gap-4\">\n                          {/* Company Logo Placeholder */}\n                          <div className=\"w-12 h-12 rounded-lg bg-gradient-to-br from-primary to-purple-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0\">\n                            {exp.company.split(' ').map(word => word[0]).join('')}\n                          </div>\n\n                          <div className=\"flex-1\">\n                            <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2\">\n                              <h4 className=\"text-lg font-bold text-foreground\">\n                                {exp.position}\n                              </h4>\n                              <Badge variant=\"outline\" className=\"w-fit\">\n                                <Calendar className=\"mr-1 h-3 w-3\" />\n                                {exp.duration}\n                              </Badge>\n                            </div>\n\n                            <p className=\"text-primary font-medium mb-3\">\n                              {exp.company}\n                            </p>\n\n                            <ul className=\"space-y-2 mb-4\">\n                              {exp.description.map((item, idx) => (\n                                <li key={idx} className=\"text-sm text-muted-foreground flex items-start gap-2\">\n                                  <span className=\"w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0\" />\n                                  {item}\n                                </li>\n                              ))}\n                            </ul>\n\n                            <div className=\"flex flex-wrap gap-1\">\n                              {exp.technologies.map((tech) => (\n                                <Badge key={tech} variant=\"secondary\" className=\"text-xs\">\n                                  {tech}\n                                </Badge>\n                              ))}\n                            </div>\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Education */}\n            <motion.div variants={itemVariants}>\n              <div className=\"flex items-center gap-3 mb-8\">\n                <div className=\"p-2 rounded-lg bg-primary/10\">\n                  <GraduationCap className=\"h-6 w-6 text-primary\" />\n                </div>\n                <h3 className=\"text-2xl font-bold\">Education</h3>\n              </div>\n\n              <div className=\"space-y-6\">\n                {education.map((edu, index) => (\n                  <motion.div\n                    key={edu.id}\n                    initial={{ opacity: 0, x: 20 }}\n                    whileInView={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                  >\n                    <Card className=\"glass border-0 shadow-lg hover:shadow-xl transition-all duration-300\">\n                      <CardContent className=\"p-6\">\n                        <div className=\"flex items-start gap-4\">\n                          {/* Institution Logo Placeholder */}\n                          <div className=\"w-12 h-12 rounded-lg bg-gradient-to-br from-purple-600 to-primary flex items-center justify-center text-white font-bold text-lg flex-shrink-0\">\n                            {edu.institution.split(' ').map(word => word[0]).join('')}\n                          </div>\n\n                          <div className=\"flex-1\">\n                            <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2\">\n                              <h4 className=\"text-lg font-bold text-foreground\">\n                                {edu.degree}\n                              </h4>\n                              <Badge variant=\"outline\" className=\"w-fit\">\n                                <Calendar className=\"mr-1 h-3 w-3\" />\n                                {edu.duration}\n                              </Badge>\n                            </div>\n\n                            <p className=\"text-primary font-medium mb-1\">\n                              {edu.field}\n                            </p>\n\n                            <p className=\"text-sm text-muted-foreground mb-3\">\n                              {edu.institution}\n                            </p>\n\n                            {edu.description && (\n                              <p className=\"text-sm text-muted-foreground mb-3\">\n                                {edu.description}\n                              </p>\n                            )}\n\n                            {edu.gpa && (\n                              <Badge variant=\"secondary\" className=\"text-xs\">\n                                GPA: {edu.gpa}\n                              </Badge>\n                            )}\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  </motion.div>\n                ))}\n              </div>\n\n              {/* Additional Certifications */}\n              <div className=\"mt-12\">\n                <h4 className=\"text-lg font-bold mb-4 flex items-center gap-2\">\n                  <span className=\"w-2 h-2 rounded-full bg-primary\" />\n                  Certifications & Achievements\n                </h4>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center gap-3 p-3 rounded-lg bg-muted/50\">\n                    <Badge variant=\"outline\">2023</Badge>\n                    <span className=\"text-sm\">AWS Certified Solutions Architect</span>\n                  </div>\n                  <div className=\"flex items-center gap-3 p-3 rounded-lg bg-muted/50\">\n                    <Badge variant=\"outline\">2022</Badge>\n                    <span className=\"text-sm\">Google Cloud Professional Developer</span>\n                  </div>\n                  <div className=\"flex items-center gap-3 p-3 rounded-lg bg-muted/50\">\n                    <Badge variant=\"outline\">2021</Badge>\n                    <span className=\"text-sm\">React Developer Certification</span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAPA;;;;;;;AASA,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,eAAe;YACf,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,GAAG;QAAI,SAAS;IAAE;IAC5B,SAAS;QACP,GAAG;QACH,SAAS;QACT,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAa,WAAU;kBACjC,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAGpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;;oCAAkD;kDAC3D,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAErC,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAKjE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;;;;;;;kDAGrC,8OAAC;wCAAI,WAAU;kDACZ,yHAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,KAAK,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,UAAU;oDAAE,MAAM;gDAAK;0DAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;8DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAI,WAAU;8EACZ,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;8EAGpD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FACX,IAAI,QAAQ;;;;;;8FAEf,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;sGACjC,8OAAC,0MAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;wFACnB,IAAI,QAAQ;;;;;;;;;;;;;sFAIjB,8OAAC;4EAAE,WAAU;sFACV,IAAI,OAAO;;;;;;sFAGd,8OAAC;4EAAG,WAAU;sFACX,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,oBAC1B,8OAAC;oFAAa,WAAU;;sGACtB,8OAAC;4FAAK,WAAU;;;;;;wFACf;;mFAFM;;;;;;;;;;sFAOb,8OAAC;4EAAI,WAAU;sFACZ,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,iIAAA,CAAA,QAAK;oFAAY,SAAQ;oFAAY,WAAU;8FAC7C;mFADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAxCnB,IAAI,EAAE;;;;;;;;;;;;;;;;0CAuDnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAE3B,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;;;;;;;kDAGrC,8OAAC;wCAAI,WAAU;kDACZ,yHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,UAAU;oDAAE,MAAM;gDAAK;0DAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;8DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAI,WAAU;8EACZ,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;8EAGxD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FACX,IAAI,MAAM;;;;;;8FAEb,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;sGACjC,8OAAC,0MAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;wFACnB,IAAI,QAAQ;;;;;;;;;;;;;sFAIjB,8OAAC;4EAAE,WAAU;sFACV,IAAI,KAAK;;;;;;sFAGZ,8OAAC;4EAAE,WAAU;sFACV,IAAI,WAAW;;;;;;wEAGjB,IAAI,WAAW,kBACd,8OAAC;4EAAE,WAAU;sFACV,IAAI,WAAW;;;;;;wEAInB,IAAI,GAAG,kBACN,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAY,WAAU;;gFAAU;gFACvC,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAzCpB,IAAI,EAAE;;;;;;;;;;kDAqDjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;;;;;;oDAAoC;;;;;;;0DAGtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9C", "debugId": null}}, {"offset": {"line": 3904, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/data/projects.ts"], "sourcesContent": ["import { Project } from \"@/types\";\n\nexport const projects: Project[] = [\n  {\n    id: \"1\",\n    title: \"E-Commerce Platform\",\n    description: \"A full-stack e-commerce platform with modern UI/UX, payment integration, and admin dashboard.\",\n    longDescription: \"Built with Next.js, TypeScript, and Tailwind CSS. Features include user authentication, product management, shopping cart, payment processing with Stripe, order tracking, and comprehensive admin dashboard. Implemented with PostgreSQL database and deployed on Vercel.\",\n    image: \"/projects/ecommerce.jpg\",\n    technologies: [\"Next.js\", \"TypeScript\", \"Tailwind CSS\", \"PostgreSQL\", \"Stripe\", \"Prisma\"],\n    githubUrl: \"https://github.com/yourusername/ecommerce-platform\",\n    liveUrl: \"https://ecommerce-demo.vercel.app\",\n    category: \"web\",\n    featured: true,\n    status: \"completed\"\n  },\n  {\n    id: \"2\",\n    title: \"AI Chat Application\",\n    description: \"Real-time chat application with AI-powered responses and modern messaging features.\",\n    longDescription: \"A sophisticated chat application built with React, Socket.io, and OpenAI API. Features include real-time messaging, AI-powered responses, file sharing, emoji reactions, and user presence indicators. Implemented with MongoDB for message storage and Redis for session management.\",\n    image: \"/projects/ai-chat.jpg\",\n    technologies: [\"React\", \"Node.js\", \"Socket.io\", \"OpenAI API\", \"MongoDB\", \"Redis\"],\n    githubUrl: \"https://github.com/yourusername/ai-chat-app\",\n    liveUrl: \"https://ai-chat-demo.vercel.app\",\n    category: \"ai\",\n    featured: true,\n    status: \"completed\"\n  },\n  {\n    id: \"3\",\n    title: \"Task Management Dashboard\",\n    description: \"Collaborative task management tool with team features and project tracking.\",\n    longDescription: \"A comprehensive project management dashboard built with Vue.js and Laravel. Features include task creation and assignment, team collaboration, project timelines, file attachments, and detailed analytics. Integrated with third-party tools like Slack and GitHub.\",\n    image: \"/projects/task-manager.jpg\",\n    technologies: [\"Vue.js\", \"Laravel\", \"MySQL\", \"Docker\", \"AWS\"],\n    githubUrl: \"https://github.com/yourusername/task-manager\",\n    liveUrl: \"https://taskmanager-demo.vercel.app\",\n    category: \"web\",\n    featured: false,\n    status: \"completed\"\n  },\n  {\n    id: \"4\",\n    title: \"Mobile Fitness App\",\n    description: \"Cross-platform mobile app for fitness tracking and workout planning.\",\n    longDescription: \"A React Native application for fitness enthusiasts. Features include workout tracking, exercise library, progress analytics, social features, and integration with wearable devices. Built with Firebase for backend services and real-time data synchronization.\",\n    image: \"/projects/fitness-app.jpg\",\n    technologies: [\"React Native\", \"Firebase\", \"TypeScript\", \"Expo\"],\n    githubUrl: \"https://github.com/yourusername/fitness-app\",\n    category: \"mobile\",\n    featured: true,\n    status: \"in-progress\"\n  },\n  {\n    id: \"5\",\n    title: \"Data Visualization Platform\",\n    description: \"Interactive dashboard for data analysis and visualization with real-time updates.\",\n    longDescription: \"A powerful data visualization platform built with D3.js and React. Features include interactive charts, real-time data updates, custom dashboard creation, data export capabilities, and integration with various data sources including APIs and databases.\",\n    image: \"/projects/data-viz.jpg\",\n    technologies: [\"React\", \"D3.js\", \"Python\", \"FastAPI\", \"PostgreSQL\"],\n    githubUrl: \"https://github.com/yourusername/data-viz-platform\",\n    liveUrl: \"https://dataviz-demo.vercel.app\",\n    category: \"web\",\n    featured: false,\n    status: \"completed\"\n  },\n  {\n    id: \"6\",\n    title: \"Blockchain Voting System\",\n    description: \"Secure and transparent voting system built on blockchain technology.\",\n    longDescription: \"A decentralized voting application built with Solidity smart contracts and React frontend. Features include voter registration, secure ballot casting, real-time vote counting, and transparent result verification. Deployed on Ethereum testnet with MetaMask integration.\",\n    image: \"/projects/blockchain-voting.jpg\",\n    technologies: [\"Solidity\", \"React\", \"Web3.js\", \"Ethereum\", \"IPFS\"],\n    githubUrl: \"https://github.com/yourusername/blockchain-voting\",\n    category: \"web\",\n    featured: false,\n    status: \"planned\"\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,cAAc;YAAC;YAAW;YAAc;YAAgB;YAAc;YAAU;SAAS;QACzF,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,cAAc;YAAC;YAAS;YAAW;YAAa;YAAc;YAAW;SAAQ;QACjF,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,cAAc;YAAC;YAAU;YAAW;YAAS;YAAU;SAAM;QAC7D,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,cAAc;YAAC;YAAgB;YAAY;YAAc;SAAO;QAChE,WAAW;QACX,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,cAAc;YAAC;YAAS;YAAS;YAAU;YAAW;SAAa;QACnE,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,cAAc;YAAC;YAAY;YAAS;YAAW;YAAY;SAAO;QAClE,WAAW;QACX,UAAU;QACV,UAAU;QACV,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 4028, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/projects.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { ExternalLink, Github, Filter } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON>, CardContent, CardHeader } from \"@/components/ui/card\";\nimport { projects } from \"@/data/projects\";\nimport { Project } from \"@/types\";\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      delayChildren: 0.3,\n      staggerChildren: 0.1,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { y: 20, opacity: 0 },\n  visible: {\n    y: 0,\n    opacity: 1,\n    transition: {\n      duration: 0.5,\n    },\n  },\n};\n\nconst categories = [\"all\", \"web\", \"mobile\", \"ai\", \"desktop\", \"other\"];\n\nexport function Projects() {\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\n  const [filteredProjects, setFilteredProjects] = useState(projects);\n\n  const handleCategoryChange = (category: string) => {\n    setSelectedCategory(category);\n    if (category === \"all\") {\n      setFilteredProjects(projects);\n    } else {\n      setFilteredProjects(projects.filter(project => project.category === category));\n    }\n  };\n\n  return (\n    <section id=\"projects\" className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-4\">\n              My <span className=\"gradient-text\">Projects</span>\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              A collection of projects that showcase my skills and passion for development\n            </p>\n          </motion.div>\n\n          {/* Category Filter */}\n          <motion.div variants={itemVariants} className=\"flex flex-wrap justify-center gap-2 mb-12\">\n            {categories.map((category) => (\n              <Button\n                key={category}\n                variant={selectedCategory === category ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => handleCategoryChange(category)}\n                className={`capitalize ${\n                  selectedCategory === category \n                    ? \"bg-primary text-primary-foreground\" \n                    : \"hover:bg-primary hover:text-primary-foreground\"\n                } transition-all duration-300`}\n              >\n                <Filter className=\"mr-2 h-4 w-4\" />\n                {category}\n              </Button>\n            ))}\n          </motion.div>\n\n          {/* Projects Grid */}\n          <motion.div \n            layout\n            className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n          >\n            <AnimatePresence mode=\"wait\">\n              {filteredProjects.map((project, index) => (\n                <motion.div\n                  key={project.id}\n                  layout\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 0.8 }}\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\n                  whileHover={{ y: -5 }}\n                  className=\"group\"\n                >\n                  <Card className=\"h-full glass border-0 shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden\">\n                    {/* Project Image */}\n                    <div className=\"relative h-48 bg-gradient-to-br from-primary/20 to-purple-500/20 overflow-hidden\">\n                      <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\" />\n                      <div className=\"absolute top-4 right-4\">\n                        {project.featured && (\n                          <Badge className=\"bg-primary text-primary-foreground\">\n                            Featured\n                          </Badge>\n                        )}\n                      </div>\n                      <div className=\"absolute bottom-4 left-4 right-4\">\n                        <div className=\"flex gap-2\">\n                          {project.githubUrl && (\n                            <Button\n                              size=\"sm\"\n                              variant=\"secondary\"\n                              className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                              asChild\n                            >\n                              <a\n                                href={project.githubUrl}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                              >\n                                <Github className=\"h-4 w-4\" />\n                              </a>\n                            </Button>\n                          )}\n                          {project.liveUrl && (\n                            <Button\n                              size=\"sm\"\n                              className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                              asChild\n                            >\n                              <a\n                                href={project.liveUrl}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                              >\n                                <ExternalLink className=\"h-4 w-4\" />\n                              </a>\n                            </Button>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n\n                    <CardHeader className=\"pb-3\">\n                      <div className=\"flex items-center justify-between\">\n                        <h3 className=\"text-xl font-bold group-hover:gradient-text transition-all duration-300\">\n                          {project.title}\n                        </h3>\n                        <Badge variant=\"outline\" className=\"capitalize\">\n                          {project.category}\n                        </Badge>\n                      </div>\n                    </CardHeader>\n\n                    <CardContent className=\"pt-0\">\n                      <p className=\"text-muted-foreground mb-4 line-clamp-3\">\n                        {project.description}\n                      </p>\n\n                      {/* Technologies */}\n                      <div className=\"flex flex-wrap gap-1 mb-4\">\n                        {project.technologies.slice(0, 4).map((tech) => (\n                          <Badge\n                            key={tech}\n                            variant=\"secondary\"\n                            className=\"text-xs\"\n                          >\n                            {tech}\n                          </Badge>\n                        ))}\n                        {project.technologies.length > 4 && (\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            +{project.technologies.length - 4}\n                          </Badge>\n                        )}\n                      </div>\n\n                      {/* Status */}\n                      <div className=\"flex items-center justify-between\">\n                        <Badge\n                          variant={\n                            project.status === \"completed\"\n                              ? \"default\"\n                              : project.status === \"in-progress\"\n                              ? \"secondary\"\n                              : \"outline\"\n                          }\n                          className=\"capitalize\"\n                        >\n                          {project.status.replace(\"-\", \" \")}\n                        </Badge>\n                        \n                        <div className=\"flex gap-2\">\n                          {project.githubUrl && (\n                            <Button\n                              size=\"sm\"\n                              variant=\"ghost\"\n                              className=\"h-8 w-8 p-0\"\n                              asChild\n                            >\n                              <a\n                                href={project.githubUrl}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                              >\n                                <Github className=\"h-4 w-4\" />\n                              </a>\n                            </Button>\n                          )}\n                          {project.liveUrl && (\n                            <Button\n                              size=\"sm\"\n                              variant=\"ghost\"\n                              className=\"h-8 w-8 p-0\"\n                              asChild\n                            >\n                              <a\n                                href={project.liveUrl}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                              >\n                                <ExternalLink className=\"h-4 w-4\" />\n                              </a>\n                            </Button>\n                          )}\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              ))}\n            </AnimatePresence>\n          </motion.div>\n\n          {/* View More Button */}\n          {filteredProjects.length === 0 && (\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              className=\"text-center py-12\"\n            >\n              <p className=\"text-muted-foreground\">No projects found in this category.</p>\n            </motion.div>\n          )}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAWA,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,eAAe;YACf,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,GAAG;QAAI,SAAS;IAAE;IAC5B,SAAS;QACP,GAAG;QACH,SAAS;QACT,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEA,MAAM,aAAa;IAAC;IAAO;IAAO;IAAU;IAAM;IAAW;CAAQ;AAE9D,SAAS;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,uHAAA,CAAA,WAAQ;IAEjE,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,IAAI,aAAa,OAAO;YACtB,oBAAoB,uHAAA,CAAA,WAAQ;QAC9B,OAAO;YACL,oBAAoB,uHAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;QACtE;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAGpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;;oCAAkD;kDAC3D,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAErC,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAMjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;kCAC3C,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAS,qBAAqB,WAAW,YAAY;gCACrD,MAAK;gCACL,SAAS,IAAM,qBAAqB;gCACpC,WAAW,CAAC,WAAW,EACrB,qBAAqB,WACjB,uCACA,iDACL,4BAA4B,CAAC;;kDAE9B,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB;;+BAXI;;;;;;;;;;kCAiBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,MAAM;wBACN,WAAU;kCAEV,cAAA,8OAAC,yLAAA,CAAA,kBAAe;4BAAC,MAAK;sCACnB,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,MAAM;oCACN,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,MAAM;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAC/B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;8CAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DAEd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,QAAQ,kBACf,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAqC;;;;;;;;;;;kEAK1D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;gEACZ,QAAQ,SAAS,kBAChB,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,WAAU;oEACV,OAAO;8EAEP,cAAA,8OAAC;wEACC,MAAM,QAAQ,SAAS;wEACvB,QAAO;wEACP,KAAI;kFAEJ,cAAA,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;gEAIvB,QAAQ,OAAO,kBACd,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,WAAU;oEACV,OAAO;8EAEP,cAAA,8OAAC;wEACC,MAAM,QAAQ,OAAO;wEACrB,QAAO;wEACP,KAAI;kFAEJ,cAAA,8OAAC,sNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQpC,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;0DAKvB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;kEAItB,8OAAC;wDAAI,WAAU;;4DACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC,iIAAA,CAAA,QAAK;oEAEJ,SAAQ;oEACR,WAAU;8EAET;mEAJI;;;;;4DAOR,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAY,WAAU;;oEAAU;oEAC3C,QAAQ,YAAY,CAAC,MAAM,GAAG;;;;;;;;;;;;;kEAMtC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SACE,QAAQ,MAAM,KAAK,cACf,YACA,QAAQ,MAAM,KAAK,gBACnB,cACA;gEAEN,WAAU;0EAET,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;0EAG/B,8OAAC;gEAAI,WAAU;;oEACZ,QAAQ,SAAS,kBAChB,8OAAC,kIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,WAAU;wEACV,OAAO;kFAEP,cAAA,8OAAC;4EACC,MAAM,QAAQ,SAAS;4EACvB,QAAO;4EACP,KAAI;sFAEJ,cAAA,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;oEAIvB,QAAQ,OAAO,kBACd,8OAAC,kIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,WAAU;wEACV,OAAO;kFAEP,cAAA,8OAAC;4EACC,MAAM,QAAQ,OAAO;4EACrB,QAAO;4EACP,KAAI;sFAEJ,cAAA,8OAAC,sNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAvIjC,QAAQ,EAAE;;;;;;;;;;;;;;;oBAqJtB,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,WAAU;kCAEV,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}, {"offset": {"line": 4525, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/resume.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Download, Calendar, MapPin, Mail, Phone, Award, Briefcase, GraduationCap, Code, Star } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { personalInfo } from \"@/data/social\";\n\nconst resumeData = {\n  summary: \"Passionate Full Stack Developer with 3+ years of experience building dynamic and responsive web applications. Skilled in modern JavaScript frameworks, backend technologies, and UI/UX design with a focus on creating user-friendly, scalable solutions.\",\n  \n  experience: [\n    {\n      title: \"Full Stack Developer\",\n      company: \"Tech Solutions Inc.\",\n      location: \"Remote\",\n      period: \"2022 - Present\",\n      description: [\n        \"Developed and maintained 15+ responsive web applications using React.js and Next.js\",\n        \"Built RESTful APIs with Node.js and Express.js, serving 10,000+ daily users\",\n        \"Implemented modern UI/UX designs with Tailwind CSS and Framer Motion\",\n        \"Collaborated with cross-functional teams to deliver projects 20% ahead of schedule\"\n      ]\n    },\n    {\n      title: \"Frontend Developer\",\n      company: \"Digital Agency Pro\",\n      location: \"Hybrid\",\n      period: \"2021 - 2022\",\n      description: [\n        \"Created pixel-perfect responsive websites for 25+ clients\",\n        \"Optimized website performance, improving load times by 40%\",\n        \"Integrated third-party APIs and payment gateways\",\n        \"Mentored 3 junior developers in modern frontend practices\"\n      ]\n    },\n    {\n      title: \"Web Developer Intern\",\n      company: \"StartUp Hub\",\n      location: \"On-site\",\n      period: \"2021\",\n      description: [\n        \"Assisted in developing company website using HTML, CSS, and JavaScript\",\n        \"Learned version control with Git and collaborative development\",\n        \"Participated in code reviews and agile development processes\",\n        \"Contributed to 5+ successful project deliveries\"\n      ]\n    }\n  ],\n\n  education: [\n    {\n      degree: \"Bachelor of Computer Applications (BCA)\",\n      institution: \"University of Technology\",\n      location: \"India\",\n      period: \"2019 - 2022\",\n      grade: \"First Class with Distinction\",\n      description: \"Specialized in Software Development and Web Technologies\"\n    },\n    {\n      degree: \"Higher Secondary Certificate\",\n      institution: \"Modern High School\",\n      location: \"India\", \n      period: \"2017 - 2019\",\n      grade: \"85%\",\n      description: \"Science Stream with Computer Science\"\n    }\n  ],\n\n  certifications: [\n    {\n      name: \"React Developer Certification\",\n      issuer: \"Meta\",\n      date: \"2023\",\n      credentialId: \"META-REACT-2023\"\n    },\n    {\n      name: \"Full Stack Web Development\",\n      issuer: \"freeCodeCamp\",\n      date: \"2022\",\n      credentialId: \"FCC-FULLSTACK-2022\"\n    },\n    {\n      name: \"JavaScript Algorithms and Data Structures\",\n      issuer: \"freeCodeCamp\",\n      date: \"2021\",\n      credentialId: \"FCC-JS-2021\"\n    }\n  ],\n\n  skills: {\n    technical: [\n      \"JavaScript (ES6+)\", \"TypeScript\", \"React.js\", \"Next.js\", \"Node.js\", \"Express.js\",\n      \"HTML5\", \"CSS3\", \"Tailwind CSS\", \"MongoDB\", \"MySQL\", \"Git\", \"Docker\"\n    ],\n    soft: [\n      \"Problem Solving\", \"Team Collaboration\", \"Communication\", \"Time Management\",\n      \"Adaptability\", \"Critical Thinking\", \"Leadership\", \"Creativity\"\n    ]\n  }\n};\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.1\n    }\n  }\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 20 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6\n    }\n  }\n};\n\nexport function Resume() {\n  const handleDownloadResume = () => {\n    // Create a link to download resume PDF\n    const link = document.createElement('a');\n    link.href = '/resume.pdf'; // You'll need to add your resume PDF to the public folder\n    link.download = 'Bhupender_Yadav_Resume.pdf';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  return (\n    <section id=\"resume\" className=\"py-20 bg-gradient-to-br from-slate-800 via-slate-900 to-slate-800\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl lg:text-5xl font-bold mb-6\"\n          >\n            <span className=\"bg-gradient-to-r from-cyan-400 via-teal-400 to-blue-500 bg-clip-text text-transparent\">\n              Resume\n            </span>\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-lg text-slate-300 max-w-2xl mx-auto mb-8\"\n          >\n            Download my complete resume or explore my professional journey below\n          </motion.p>\n          <motion.div variants={itemVariants}>\n            <Button\n              onClick={handleDownloadResume}\n              size=\"lg\"\n              className=\"bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-semibold px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105\"\n            >\n              <Download className=\"w-5 h-5 mr-2\" />\n              Download Resume\n            </Button>\n          </motion.div>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Left Column - Personal Info & Summary */}\n          <motion.div\n            variants={itemVariants}\n            className=\"lg:col-span-1 space-y-6\"\n          >\n            {/* Personal Information */}\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10\">\n              <h3 className=\"text-xl font-bold text-white mb-4 flex items-center\">\n                <Award className=\"w-5 h-5 mr-2 text-cyan-400\" />\n                Personal Information\n              </h3>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center text-slate-300\">\n                  <Mail className=\"w-4 h-4 mr-3 text-cyan-400\" />\n                  <span className=\"text-sm\">{personalInfo.email}</span>\n                </div>\n                <div className=\"flex items-center text-slate-300\">\n                  <Phone className=\"w-4 h-4 mr-3 text-green-400\" />\n                  <span className=\"text-sm\">{personalInfo.phone}</span>\n                </div>\n                <div className=\"flex items-center text-slate-300\">\n                  <MapPin className=\"w-4 h-4 mr-3 text-red-400\" />\n                  <span className=\"text-sm\">{personalInfo.location}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Professional Summary */}\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10\">\n              <h3 className=\"text-xl font-bold text-white mb-4\">Professional Summary</h3>\n              <p className=\"text-slate-300 text-sm leading-relaxed\">{resumeData.summary}</p>\n            </div>\n\n            {/* Skills */}\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10\">\n              <h3 className=\"text-xl font-bold text-white mb-4 flex items-center\">\n                <Code className=\"w-5 h-5 mr-2 text-purple-400\" />\n                Key Skills\n              </h3>\n              <div className=\"space-y-4\">\n                <div>\n                  <h4 className=\"text-cyan-400 font-semibold mb-2 text-sm\">Technical Skills</h4>\n                  <div className=\"flex flex-wrap gap-1\">\n                    {resumeData.skills.technical.map((skill, index) => (\n                      <span\n                        key={index}\n                        className=\"px-2 py-1 bg-white/10 text-slate-300 text-xs rounded-md border border-white/20\"\n                      >\n                        {skill}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n                <div>\n                  <h4 className=\"text-green-400 font-semibold mb-2 text-sm\">Soft Skills</h4>\n                  <div className=\"flex flex-wrap gap-1\">\n                    {resumeData.skills.soft.map((skill, index) => (\n                      <span\n                        key={index}\n                        className=\"px-2 py-1 bg-white/10 text-slate-300 text-xs rounded-md border border-white/20\"\n                      >\n                        {skill}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Right Column - Experience, Education, Certifications */}\n          <motion.div\n            variants={itemVariants}\n            className=\"lg:col-span-2 space-y-8\"\n          >\n            {/* Professional Experience */}\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10\">\n              <h3 className=\"text-2xl font-bold text-white mb-6 flex items-center\">\n                <Briefcase className=\"w-6 h-6 mr-3 text-cyan-400\" />\n                Professional Experience\n              </h3>\n              <div className=\"space-y-6\">\n                {resumeData.experience.map((exp, index) => (\n                  <div key={index} className=\"border-l-2 border-cyan-400 pl-6 relative\">\n                    <div className=\"absolute -left-2 top-0 w-4 h-4 bg-cyan-400 rounded-full\"></div>\n                    <div className=\"mb-2\">\n                      <h4 className=\"text-lg font-semibold text-white\">{exp.title}</h4>\n                      <p className=\"text-cyan-400 font-medium\">{exp.company}</p>\n                      <div className=\"flex items-center text-slate-400 text-sm mt-1\">\n                        <Calendar className=\"w-4 h-4 mr-1\" />\n                        <span className=\"mr-4\">{exp.period}</span>\n                        <MapPin className=\"w-4 h-4 mr-1\" />\n                        <span>{exp.location}</span>\n                      </div>\n                    </div>\n                    <ul className=\"space-y-1\">\n                      {exp.description.map((item, idx) => (\n                        <li key={idx} className=\"text-slate-300 text-sm flex items-start\">\n                          <Star className=\"w-3 h-3 mr-2 mt-1 text-yellow-400 flex-shrink-0\" />\n                          {item}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Education */}\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10\">\n              <h3 className=\"text-2xl font-bold text-white mb-6 flex items-center\">\n                <GraduationCap className=\"w-6 h-6 mr-3 text-green-400\" />\n                Education\n              </h3>\n              <div className=\"space-y-4\">\n                {resumeData.education.map((edu, index) => (\n                  <div key={index} className=\"border-l-2 border-green-400 pl-6 relative\">\n                    <div className=\"absolute -left-2 top-0 w-4 h-4 bg-green-400 rounded-full\"></div>\n                    <h4 className=\"text-lg font-semibold text-white\">{edu.degree}</h4>\n                    <p className=\"text-green-400 font-medium\">{edu.institution}</p>\n                    <div className=\"flex items-center text-slate-400 text-sm mt-1\">\n                      <Calendar className=\"w-4 h-4 mr-1\" />\n                      <span className=\"mr-4\">{edu.period}</span>\n                      <MapPin className=\"w-4 h-4 mr-1\" />\n                      <span className=\"mr-4\">{edu.location}</span>\n                      <span className=\"text-yellow-400\">Grade: {edu.grade}</span>\n                    </div>\n                    <p className=\"text-slate-300 text-sm mt-2\">{edu.description}</p>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Certifications */}\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10\">\n              <h3 className=\"text-2xl font-bold text-white mb-6 flex items-center\">\n                <Award className=\"w-6 h-6 mr-3 text-purple-400\" />\n                Certifications\n              </h3>\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                {resumeData.certifications.map((cert, index) => (\n                  <div key={index} className=\"bg-white/5 rounded-xl p-4 border border-white/20\">\n                    <h4 className=\"text-white font-semibold mb-1\">{cert.name}</h4>\n                    <p className=\"text-purple-400 text-sm font-medium\">{cert.issuer}</p>\n                    <p className=\"text-slate-400 text-xs mt-1\">Issued: {cert.date}</p>\n                    <p className=\"text-slate-500 text-xs\">ID: {cert.credentialId}</p>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAOA,MAAM,aAAa;IACjB,SAAS;IAET,YAAY;QACV;YACE,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,aAAa;gBACX;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,aAAa;gBACX;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,aAAa;gBACX;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,WAAW;QACT;YACE,QAAQ;YACR,aAAa;YACb,UAAU;YACV,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,QAAQ;YACR,aAAa;YACb,UAAU;YACV,QAAQ;YACR,OAAO;YACP,aAAa;QACf;KACD;IAED,gBAAgB;QACd;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,cAAc;QAChB;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,cAAc;QAChB;QACA;YACE,MAAM;YACN,QAAQ;YACR,MAAM;YACN,cAAc;QAChB;KACD;IAED,QAAQ;QACN,WAAW;YACT;YAAqB;YAAc;YAAY;YAAW;YAAW;YACrE;YAAS;YAAQ;YAAgB;YAAW;YAAS;YAAO;SAC7D;QACD,MAAM;YACJ;YAAmB;YAAsB;YAAiB;YAC1D;YAAgB;YAAqB;YAAc;SACpD;IACH;AACF;AAEA,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;IACd,MAAM,uBAAuB;QAC3B,uCAAuC;QACvC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,eAAe,0DAA0D;QACrF,KAAK,QAAQ,GAAG;QAChB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAK,WAAU;0CAAwF;;;;;;;;;;;sCAI1G,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;sCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,MAAK;gCACL,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAM3C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;sDAGlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAW,qHAAA,CAAA,eAAY,CAAC,KAAK;;;;;;;;;;;;8DAE/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAW,qHAAA,CAAA,eAAY,CAAC,KAAK;;;;;;;;;;;;8DAE/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAW,qHAAA,CAAA,eAAY,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;8CAMtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAA0C,WAAW,OAAO;;;;;;;;;;;;8CAI3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiC;;;;;;;sDAGnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAI,WAAU;sEACZ,WAAW,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,sBACvC,8OAAC;oEAEC,WAAU;8EAET;mEAHI;;;;;;;;;;;;;;;;8DAQb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,8OAAC;4DAAI,WAAU;sEACZ,WAAW,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,sBAClC,8OAAC;oEAEC,WAAU;8EAET;mEAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAanB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,4MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;sDAGtD,8OAAC;4CAAI,WAAU;sDACZ,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAoC,IAAI,KAAK;;;;;;8EAC3D,8OAAC;oEAAE,WAAU;8EAA6B,IAAI,OAAO;;;;;;8EACrD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;4EAAK,WAAU;sFAAQ,IAAI,MAAM;;;;;;sFAClC,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;sFAAM,IAAI,QAAQ;;;;;;;;;;;;;;;;;;sEAGvB,8OAAC;4DAAG,WAAU;sEACX,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,oBAC1B,8OAAC;oEAAa,WAAU;;sFACtB,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEACf;;mEAFM;;;;;;;;;;;mDAdL;;;;;;;;;;;;;;;;8CA0BhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAgC;;;;;;;sDAG3D,8OAAC;4CAAI,WAAU;sDACZ,WAAW,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC9B,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAG,WAAU;sEAAoC,IAAI,MAAM;;;;;;sEAC5D,8OAAC;4DAAE,WAAU;sEAA8B,IAAI,WAAW;;;;;;sEAC1D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAQ,IAAI,MAAM;;;;;;8EAClC,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;oEAAK,WAAU;8EAAQ,IAAI,QAAQ;;;;;;8EACpC,8OAAC;oEAAK,WAAU;;wEAAkB;wEAAQ,IAAI,KAAK;;;;;;;;;;;;;sEAErD,8OAAC;4DAAE,WAAU;sEAA+B,IAAI,WAAW;;;;;;;mDAXnD;;;;;;;;;;;;;;;;8CAkBhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiC;;;;;;;sDAGpD,8OAAC;4CAAI,WAAU;sDACZ,WAAW,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpC,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAG,WAAU;sEAAiC,KAAK,IAAI;;;;;;sEACxD,8OAAC;4DAAE,WAAU;sEAAuC,KAAK,MAAM;;;;;;sEAC/D,8OAAC;4DAAE,WAAU;;gEAA8B;gEAAS,KAAK,IAAI;;;;;;;sEAC7D,8OAAC;4DAAE,WAAU;;gEAAyB;gEAAK,KAAK,YAAY;;;;;;;;mDAJpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc5B", "debugId": null}}, {"offset": {"line": 5375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/youtube.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Play, Eye, ThumbsUp, Calendar, ExternalLink } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nconst youtubeVideos = [\n  {\n    id: \"1\",\n    title: \"Building a Modern Portfolio with Next.js & Tailwind CSS\",\n    description: \"Complete tutorial on creating a responsive portfolio website using Next.js, TypeScript, and Tailwind CSS with modern animations.\",\n    thumbnail: \"/api/placeholder/400/225\",\n    views: \"15.2K\",\n    likes: \"892\",\n    duration: \"28:45\",\n    publishedAt: \"2 weeks ago\",\n    url: \"https://youtube.com/watch?v=example1\"\n  },\n  {\n    id: \"2\",\n    title: \"React Hooks Explained: useState, useEffect & Custom Hooks\",\n    description: \"Deep dive into React Hooks with practical examples and best practices for modern React development.\",\n    thumbnail: \"/api/placeholder/400/225\",\n    views: \"23.8K\",\n    likes: \"1.2K\",\n    duration: \"35:12\",\n    publishedAt: \"1 month ago\",\n    url: \"https://youtube.com/watch?v=example2\"\n  },\n  {\n    id: \"3\",\n    title: \"Full Stack MERN App: From Concept to Deployment\",\n    description: \"Build and deploy a complete MERN stack application with authentication, CRUD operations, and modern UI.\",\n    thumbnail: \"/api/placeholder/400/225\",\n    views: \"31.5K\",\n    likes: \"1.8K\",\n    duration: \"1:42:30\",\n    publishedAt: \"2 months ago\",\n    url: \"https://youtube.com/watch?v=example3\"\n  },\n  {\n    id: \"4\",\n    title: \"CSS Grid vs Flexbox: When to Use What?\",\n    description: \"Comprehensive comparison of CSS Grid and Flexbox with real-world examples and use cases.\",\n    thumbnail: \"/api/placeholder/400/225\",\n    views: \"18.7K\",\n    likes: \"956\",\n    duration: \"22:18\",\n    publishedAt: \"3 months ago\",\n    url: \"https://youtube.com/watch?v=example4\"\n  },\n  {\n    id: \"5\",\n    title: \"JavaScript ES6+ Features Every Developer Should Know\",\n    description: \"Modern JavaScript features including arrow functions, destructuring, async/await, and more.\",\n    thumbnail: \"/api/placeholder/400/225\",\n    views: \"27.3K\",\n    likes: \"1.5K\",\n    duration: \"41:22\",\n    publishedAt: \"4 months ago\",\n    url: \"https://youtube.com/watch?v=example5\"\n  },\n  {\n    id: \"6\",\n    title: \"UI/UX Design Principles for Developers\",\n    description: \"Essential design principles that every developer should understand to create better user interfaces.\",\n    thumbnail: \"/api/placeholder/400/225\",\n    views: \"12.9K\",\n    likes: \"743\",\n    duration: \"33:55\",\n    publishedAt: \"5 months ago\",\n    url: \"https://youtube.com/watch?v=example6\"\n  }\n];\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.1\n    }\n  }\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 20 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6\n    }\n  }\n};\n\nexport function YouTube() {\n  return (\n    <section id=\"youtube\" className=\"py-20 bg-gradient-to-br from-slate-800 via-slate-900 to-slate-800\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl lg:text-5xl font-bold mb-6\"\n          >\n            <span className=\"bg-gradient-to-r from-red-400 via-pink-400 to-purple-500 bg-clip-text text-transparent\">\n              YouTube Channel\n            </span>\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-lg text-slate-300 max-w-2xl mx-auto mb-8\"\n          >\n            Educational content about web development, programming tutorials, and tech insights\n          </motion.p>\n          <motion.div variants={itemVariants}>\n            <Button\n              size=\"lg\"\n              className=\"bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white font-semibold px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105\"\n            >\n              <Play className=\"w-5 h-5 mr-2\" />\n              Subscribe to Channel\n            </Button>\n          </motion.div>\n        </motion.div>\n\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {youtubeVideos.map((video) => (\n            <motion.div\n              key={video.id}\n              variants={itemVariants}\n              whileHover={{ scale: 1.02 }}\n              className=\"bg-white/5 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/10 hover:border-white/20 transition-all duration-300 group\"\n            >\n              <div className=\"relative\">\n                <div className=\"aspect-video bg-gradient-to-br from-slate-700 to-slate-800 flex items-center justify-center\">\n                  <Play className=\"w-16 h-16 text-white/60\" />\n                </div>\n                <div className=\"absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded\">\n                  {video.duration}\n                </div>\n                <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\n                  <Button\n                    size=\"sm\"\n                    className=\"bg-red-500 hover:bg-red-600 text-white rounded-full\"\n                  >\n                    <Play className=\"w-4 h-4\" />\n                  </Button>\n                </div>\n              </div>\n\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-semibold text-white mb-2 line-clamp-2 group-hover:text-red-400 transition-colors\">\n                  {video.title}\n                </h3>\n                <p className=\"text-slate-400 text-sm mb-4 line-clamp-2\">\n                  {video.description}\n                </p>\n\n                <div className=\"flex items-center justify-between text-xs text-slate-500 mb-4\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"flex items-center\">\n                      <Eye className=\"w-3 h-3 mr-1\" />\n                      {video.views}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <ThumbsUp className=\"w-3 h-3 mr-1\" />\n                      {video.likes}\n                    </div>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <Calendar className=\"w-3 h-3 mr-1\" />\n                    {video.publishedAt}\n                  </div>\n                </div>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"w-full border-white/20 text-white hover:bg-white/10 hover:border-red-400 transition-all duration-300\"\n                >\n                  <ExternalLink className=\"w-4 h-4 mr-2\" />\n                  Watch Video\n                </Button>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        <motion.div\n          variants={itemVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"text-center mt-12\"\n        >\n          <Button\n            variant=\"outline\"\n            size=\"lg\"\n            className=\"border-white/20 text-white hover:bg-white/10 hover:border-red-400 transition-all duration-300\"\n          >\n            View All Videos\n            <ExternalLink className=\"w-5 h-5 ml-2\" />\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,OAAO;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,OAAO;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,OAAO;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,OAAO;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,OAAO;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,OAAO;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,KAAK;IACP;CACD;AAED,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAK,WAAU;0CAAyF;;;;;;;;;;;sCAI3G,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;sCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAMvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;sDACZ,MAAM,QAAQ;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAEd,8OAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;sDAGpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEACd,MAAM,KAAK;;;;;;;sEAEd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,MAAM,KAAK;;;;;;;;;;;;;8DAGhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,MAAM,WAAW;;;;;;;;;;;;;sDAItB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;2BApDxC,MAAM,EAAE;;;;;;;;;;8BA4DnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;4BACX;0CAEC,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC", "debugId": null}}, {"offset": {"line": 5797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5823, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5848, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5876, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/lib/validations.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const contactFormSchema = z.object({\n  name: z\n    .string()\n    .min(2, \"Name must be at least 2 characters\")\n    .max(50, \"Name must be less than 50 characters\"),\n  email: z\n    .string()\n    .email(\"Please enter a valid email address\")\n    .min(1, \"Email is required\"),\n  subject: z\n    .string()\n    .min(5, \"Subject must be at least 5 characters\")\n    .max(100, \"Subject must be less than 100 characters\"),\n  message: z\n    .string()\n    .min(10, \"Message must be at least 10 characters\")\n    .max(1000, \"Message must be less than 1000 characters\"),\n});\n\nexport type ContactFormData = z.infer<typeof contactFormSchema>;\n\nexport const newsletterSchema = z.object({\n  email: z\n    .string()\n    .email(\"Please enter a valid email address\")\n    .min(1, \"Email is required\"),\n});\n\nexport type NewsletterData = z.infer<typeof newsletterSchema>;\n\nexport const searchSchema = z.object({\n  query: z\n    .string()\n    .min(1, \"Search query is required\")\n    .max(100, \"Search query must be less than 100 characters\"),\n  category: z\n    .enum([\"all\", \"projects\", \"blog\", \"experience\"])\n    .optional()\n    .default(\"all\"),\n});\n\nexport type SearchData = z.infer<typeof searchSchema>;\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;AAEO,MAAM,oBAAoB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,MAAM,iLAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG,sCACP,GAAG,CAAC,IAAI;IACX,OAAO,iLAAA,CAAA,IAAC,CACL,MAAM,GACN,KAAK,CAAC,sCACN,GAAG,CAAC,GAAG;IACV,SAAS,iLAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,GAAG,yCACP,GAAG,CAAC,KAAK;IACZ,SAAS,iLAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI,0CACR,GAAG,CAAC,MAAM;AACf;AAIO,MAAM,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,OAAO,iLAAA,CAAA,IAAC,CACL,MAAM,GACN,KAAK,CAAC,sCACN,GAAG,CAAC,GAAG;AACZ;AAIO,MAAM,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,iLAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,4BACP,GAAG,CAAC,KAAK;IACZ,UAAU,iLAAA,CAAA,IAAC,CACR,IAAI,CAAC;QAAC;QAAO;QAAY;QAAQ;KAAa,EAC9C,QAAQ,GACR,OAAO,CAAC;AACb", "debugId": null}}, {"offset": {"line": 5908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/src/components/sections/contact.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Mail, Phone, MapPin, Send, Github, Linkedin, Twitter } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { contactFormSchema, ContactFormData } from \"@/lib/validations\";\nimport { personalInfo, socialLinks } from \"@/data/social\";\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      delayChildren: 0.3,\n      staggerChildren: 0.2,\n    },\n  },\n};\n\nconst itemVariants = {\n  hidden: { y: 20, opacity: 0 },\n  visible: {\n    y: 0,\n    opacity: 1,\n    transition: {\n      duration: 0.5,\n    },\n  },\n};\n\nexport function Contact() {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<\"idle\" | \"success\" | \"error\">(\"idle\");\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<ContactFormData>({\n    resolver: zodResolver(contactFormSchema),\n  });\n\n  const onSubmit = async (data: ContactFormData) => {\n    setIsSubmitting(true);\n    setSubmitStatus(\"idle\");\n\n    try {\n      // Simulate form submission\n      await new Promise((resolve) => setTimeout(resolve, 2000));\n      \n      console.log(\"Form data:\", data);\n      setSubmitStatus(\"success\");\n      reset();\n    } catch (error) {\n      setSubmitStatus(\"error\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const contactInfo = [\n    {\n      icon: Mail,\n      label: \"Email\",\n      value: personalInfo.email,\n      href: `mailto:${personalInfo.email}`,\n    },\n    {\n      icon: Phone,\n      label: \"Phone\",\n      value: personalInfo.phone,\n      href: `tel:${personalInfo.phone}`,\n    },\n    {\n      icon: MapPin,\n      label: \"Location\",\n      value: personalInfo.location,\n      href: \"#\",\n    },\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-4\">\n              Get In <span className=\"gradient-text\">Touch</span>\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Have a project in mind? Let's discuss how we can work together to bring your ideas to life.\n            </p>\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Contact Information */}\n            <motion.div variants={itemVariants}>\n              <Card className=\"glass border-0 shadow-xl h-full\">\n                <CardContent className=\"p-8\">\n                  <h3 className=\"text-2xl font-bold mb-6 gradient-text\">\n                    Let's Start a Conversation\n                  </h3>\n                  \n                  <p className=\"text-muted-foreground mb-8 leading-relaxed\">\n                    I'm always interested in hearing about new projects and opportunities. \n                    Whether you're a company looking to hire, or you're someone with an \n                    interesting project, I'd love to hear from you.\n                  </p>\n\n                  {/* Contact Info */}\n                  <div className=\"space-y-6 mb-8\">\n                    {contactInfo.map((info) => (\n                      <motion.a\n                        key={info.label}\n                        href={info.href}\n                        whileHover={{ x: 5 }}\n                        className=\"flex items-center gap-4 p-4 rounded-lg hover:bg-muted/50 transition-colors group\"\n                      >\n                        <div className=\"p-3 rounded-lg bg-primary/10 group-hover:bg-primary group-hover:text-primary-foreground transition-colors\">\n                          <info.icon className=\"h-5 w-5\" />\n                        </div>\n                        <div>\n                          <p className=\"font-medium text-foreground\">{info.label}</p>\n                          <p className=\"text-sm text-muted-foreground\">{info.value}</p>\n                        </div>\n                      </motion.a>\n                    ))}\n                  </div>\n\n                  {/* Social Links */}\n                  <div>\n                    <h4 className=\"font-semibold mb-4 text-foreground\">Follow Me</h4>\n                    <div className=\"flex gap-4\">\n                      {socialLinks.slice(0, 4).map((link) => {\n                        const IconComponent = \n                          link.icon === \"Github\" ? Github :\n                          link.icon === \"Linkedin\" ? Linkedin :\n                          link.icon === \"Twitter\" ? Twitter :\n                          Mail;\n\n                        return (\n                          <motion.a\n                            key={link.name}\n                            href={link.url}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            whileHover={{ scale: 1.1, y: -2 }}\n                            whileTap={{ scale: 0.95 }}\n                            className=\"p-3 rounded-lg bg-muted hover:bg-primary hover:text-primary-foreground transition-all duration-300 glow-hover\"\n                          >\n                            <IconComponent className=\"h-5 w-5\" />\n                            <span className=\"sr-only\">{link.name}</span>\n                          </motion.a>\n                        );\n                      })}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n\n            {/* Contact Form */}\n            <motion.div variants={itemVariants}>\n              <Card className=\"glass border-0 shadow-xl\">\n                <CardContent className=\"p-8\">\n                  <h3 className=\"text-2xl font-bold mb-6 gradient-text\">\n                    Send Me a Message\n                  </h3>\n\n                  <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                    <div className=\"grid sm:grid-cols-2 gap-4\">\n                      <div>\n                        <Label htmlFor=\"name\">Name *</Label>\n                        <Input\n                          id=\"name\"\n                          {...register(\"name\")}\n                          className=\"mt-1\"\n                          placeholder=\"Your name\"\n                        />\n                        {errors.name && (\n                          <p className=\"text-sm text-destructive mt-1\">\n                            {errors.name.message}\n                          </p>\n                        )}\n                      </div>\n\n                      <div>\n                        <Label htmlFor=\"email\">Email *</Label>\n                        <Input\n                          id=\"email\"\n                          type=\"email\"\n                          {...register(\"email\")}\n                          className=\"mt-1\"\n                          placeholder=\"<EMAIL>\"\n                        />\n                        {errors.email && (\n                          <p className=\"text-sm text-destructive mt-1\">\n                            {errors.email.message}\n                          </p>\n                        )}\n                      </div>\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"subject\">Subject *</Label>\n                      <Input\n                        id=\"subject\"\n                        {...register(\"subject\")}\n                        className=\"mt-1\"\n                        placeholder=\"What's this about?\"\n                      />\n                      {errors.subject && (\n                        <p className=\"text-sm text-destructive mt-1\">\n                          {errors.subject.message}\n                        </p>\n                      )}\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"message\">Message *</Label>\n                      <Textarea\n                        id=\"message\"\n                        {...register(\"message\")}\n                        className=\"mt-1 min-h-[120px]\"\n                        placeholder=\"Tell me about your project...\"\n                      />\n                      {errors.message && (\n                        <p className=\"text-sm text-destructive mt-1\">\n                          {errors.message.message}\n                        </p>\n                      )}\n                    </div>\n\n                    <Button\n                      type=\"submit\"\n                      disabled={isSubmitting}\n                      className=\"w-full btn-gradient\"\n                    >\n                      {isSubmitting ? (\n                        <motion.div\n                          animate={{ rotate: 360 }}\n                          transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                          className=\"mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full\"\n                        />\n                      ) : (\n                        <Send className=\"mr-2 h-4 w-4\" />\n                      )}\n                      {isSubmitting ? \"Sending...\" : \"Send Message\"}\n                    </Button>\n\n                    {/* Status Messages */}\n                    {submitStatus === \"success\" && (\n                      <motion.p\n                        initial={{ opacity: 0, y: 10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        className=\"text-sm text-green-600 text-center\"\n                      >\n                        Message sent successfully! I'll get back to you soon.\n                      </motion.p>\n                    )}\n\n                    {submitStatus === \"error\" && (\n                      <motion.p\n                        initial={{ opacity: 0, y: 10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        className=\"text-sm text-destructive text-center\"\n                      >\n                        Something went wrong. Please try again.\n                      </motion.p>\n                    )}\n                  </form>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAeA,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,eAAe;YACf,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,GAAG;QAAI,SAAS;IAAE;IAC5B,SAAS;QACP,GAAG;QACH,SAAS;QACT,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAEO,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,yHAAA,CAAA,oBAAiB;IACzC;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YAEnD,QAAQ,GAAG,CAAC,cAAc;YAC1B,gBAAgB;YAChB;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO,qHAAA,CAAA,eAAY,CAAC,KAAK;YACzB,MAAM,CAAC,OAAO,EAAE,qHAAA,CAAA,eAAY,CAAC,KAAK,EAAE;QACtC;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO,qHAAA,CAAA,eAAY,CAAC,KAAK;YACzB,MAAM,CAAC,IAAI,EAAE,qHAAA,CAAA,eAAY,CAAC,KAAK,EAAE;QACnC;QACA;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO,qHAAA,CAAA,eAAY,CAAC,QAAQ;YAC5B,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAI;;kCAGpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC;gCAAG,WAAU;;oCAAkD;kDACvD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAEzC,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAKjE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAItD,8OAAC;gDAAE,WAAU;0DAA6C;;;;;;0DAO1D,8OAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDAEP,MAAM,KAAK,IAAI;wDACf,YAAY;4DAAE,GAAG;wDAAE;wDACnB,WAAU;;0EAEV,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,KAAK,IAAI;oEAAC,WAAU;;;;;;;;;;;0EAEvB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA+B,KAAK,KAAK;;;;;;kFACtD,8OAAC;wEAAE,WAAU;kFAAiC,KAAK,KAAK;;;;;;;;;;;;;uDAVrD,KAAK,KAAK;;;;;;;;;;0DAiBrB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;kEACZ,qHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;4DAC5B,MAAM,gBACJ,KAAK,IAAI,KAAK,WAAW,sMAAA,CAAA,SAAM,GAC/B,KAAK,IAAI,KAAK,aAAa,0MAAA,CAAA,WAAQ,GACnC,KAAK,IAAI,KAAK,YAAY,wMAAA,CAAA,UAAO,GACjC,kMAAA,CAAA,OAAI;4DAEN,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gEAEP,MAAM,KAAK,GAAG;gEACd,QAAO;gEACP,KAAI;gEACJ,YAAY;oEAAE,OAAO;oEAAK,GAAG,CAAC;gEAAE;gEAChC,UAAU;oEAAE,OAAO;gEAAK;gEACxB,WAAU;;kFAEV,8OAAC;wEAAc,WAAU;;;;;;kFACzB,8OAAC;wEAAK,WAAU;kFAAW,KAAK,IAAI;;;;;;;+DAT/B,KAAK,IAAI;;;;;wDAYpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAItD,8OAAC;gDAAK,UAAU,aAAa;gDAAW,WAAU;;kEAChD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAO;;;;;;kFACtB,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACF,GAAG,SAAS,OAAO;wEACpB,WAAU;wEACV,aAAY;;;;;;oEAEb,OAAO,IAAI,kBACV,8OAAC;wEAAE,WAAU;kFACV,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;0EAK1B,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAQ;;;;;;kFACvB,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,MAAK;wEACJ,GAAG,SAAS,QAAQ;wEACrB,WAAU;wEACV,aAAY;;;;;;oEAEb,OAAO,KAAK,kBACX,8OAAC;wEAAE,WAAU;kFACV,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;kEAM7B,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACF,GAAG,SAAS,UAAU;gEACvB,WAAU;gEACV,aAAY;;;;;;4DAEb,OAAO,OAAO,kBACb,8OAAC;gEAAE,WAAU;0EACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kEAK7B,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAG;gEACF,GAAG,SAAS,UAAU;gEACvB,WAAU;gEACV,aAAY;;;;;;4DAEb,OAAO,OAAO,kBACb,8OAAC;gEAAE,WAAU;0EACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kEAK7B,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,UAAU;wDACV,WAAU;;4DAET,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,QAAQ;gEAAI;gEACvB,YAAY;oEAAE,UAAU;oEAAG,QAAQ;oEAAU,MAAM;gEAAS;gEAC5D,WAAU;;;;;qFAGZ,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAEjB,eAAe,eAAe;;;;;;;oDAIhC,iBAAiB,2BAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDACP,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,WAAU;kEACX;;;;;;oDAKF,iBAAiB,yBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDACP,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAavB", "debugId": null}}]}