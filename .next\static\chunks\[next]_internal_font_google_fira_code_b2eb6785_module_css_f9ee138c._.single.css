/* [next]/internal/font/google/fira_code_b2eb6785.module.css [app-client] (css) */
@font-face {
  font-family: Fira Code;
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url("../media/uU9NCBsR6Z2vfE9aq3bh0NSDqFGedCMX-s.a23948ee.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Fira Code;
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url("../media/uU9NCBsR6Z2vfE9aq3bh2dSDqFGedCMX-s.5470900e.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Fira Code;
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url("../media/uU9NCBsR6Z2vfE9aq3bh0dSDqFGedCMX-s.4d8f94f7.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Fira Code;
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url("../media/uU9NCBsR6Z2vfE9aq3bh3tSDqFGedCMX-s.ae6081a6.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Fira Code;
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url("../media/uU9NCBsR6Z2vfE9aq3bhZ_Wmh3mUfBsu_Q-s.dafc51b9.woff2") format("woff2");
  unicode-range: U+2000-2001, U+2004-2008, U+200A, U+23B8-23BD, U+2500-259F;
}

@font-face {
  font-family: Fira Code;
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url("../media/uU9NCBsR6Z2vfE9aq3bh09SDqFGedCMX-s.9a91385d.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Fira Code;
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url("../media/uU9NCBsR6Z2vfE9aq3bh3dSDqFGedA-s.p.1b3fc2df.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Fira Code Fallback;
  src: local(Arial);
  ascent-override: 73.56%;
  descent-override: 23.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.fira_code_b2eb6785-module__U72mxW__className {
  font-family: Fira Code, Fira Code Fallback;
  font-style: normal;
}

.fira_code_b2eb6785-module__U72mxW__variable {
  --font-fira-code: "Fira Code", "Fira Code Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_fira_code_b2eb6785_module_css_f9ee138c._.single.css.map*/