{"name": "next-seo", "version": "6.8.0", "description": "SEO plugin for Next.js projects", "main": "lib/next-seo.js", "module": "lib/next-seo.module.js", "unpkg": "lib/next-seo.umd.js", "source": "src/index.tsx", "types": "lib/index.d.ts", "files": ["lib"], "peerDependencies": {"next": "^8.1.1-canary.54 || >=9.0.0", "react": ">=16.0.0", "react-dom": ">=16.0.0"}, "scripts": {"test": "jest", "test:coverage": "jest --verbose --coverage", "test:watch": "jest --watch", "pretest:e2e:ci": "yarn e2e:build", "test:e2e": "npm-run-all --parallel e2e:dev cy:open", "test:e2e:ci": "npm-run-all --parallel --race e2e:start cy:run", "prepublishOnly": "yarn build", "prebuild": "rimraf ./lib", "format": "prettier", "table-of-contents": "doctoc README.md", "type-check": "tsc --noEmit", "type-check:watch": "yarn type-check -- --watch", "build": "yarn build:ts", "build:watch": "microbundle watch --no-sourcemap --no-compress --jsx React.createElement", "build:ts": "microbundle --no-sourcemap --no-compress --jsx React.createElement", "build:test": "npm-run-all --sequential build e2e:build", "cy:open": "cypress open", "cy:run": "cypress run", "e2e:dev": "next e2e", "e2e:devApp": "next e2eWithApp", "e2e:build": "next build e2e", "e2e:start": "next start e2e"}, "prettier": {"singleQuote": true, "arrowParens": "avoid", "trailingComma": "all"}, "author": {"name": "<PERSON>", "email": "gary<PERSON><PERSON>@outlook.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/garmeeh/next-seo.git"}, "keywords": ["next.js", "seo", "react", "node", "ssr"], "bugs": {"url": "https://github.com/garmeeh/next-seo/issues"}, "homepage": "https://github.com/garmeeh/next-seo#readme", "devDependencies": {"@babel/core": "^7.16.12", "@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.16.7", "@babel/preset-typescript": "^7.16.7", "@cypress/schema-tools": "^4.7.9", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.0", "@types/node": "^20.3.2", "@types/react": "^18.0.31", "@types/react-dom": "^18.0.11", "babel-jest": "^29.5.0", "cypress": "^12.9.0", "cypress-testing-library": "^4.0.0", "doctoc": "^2.1.0", "husky": "4.3.8", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "lint-staged": "13.2.3", "microbundle": "^0.14.2", "next": "^13.0.0", "npm-run-all": "4.1.5", "prettier": "2.8.8", "react": "^18.0.0", "react-dom": "^18.0.0", "rimraf": "4.4.1", "typescript": "^5.0.3"}, "husky": {"hooks": {"pre-commit": "yarn table-of-contents && git add README.md && lint-staged"}}, "lint-staged": {"*.{js,jsx,json,css,md,ts,tsx}": ["prettier --write", "git add"], "*.{ts,tsx}": []}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}