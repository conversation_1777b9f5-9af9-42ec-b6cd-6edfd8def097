"use strict";var e=require("react");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var r=t(e),s=e=>"checkbox"===e.type,a=e=>e instanceof Date,i=e=>null==e;const n=e=>"object"==typeof e;var o=e=>!i(e)&&!Array.isArray(e)&&n(e)&&!a(e),l=e=>o(e)&&e.target?s(e.target)?e.target.checked:e.target.value:e,u=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),d="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function c(e){let t;const r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(d&&(e instanceof Blob||s)||!r&&!o(e))return e;if(t=r?[]:{},r||(e=>{const t=e.constructor&&e.constructor.prototype;return o(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=c(e[r]));else t=e}return t}var f=e=>Array.isArray(e)?e.filter(Boolean):[],m=e=>void 0===e,y=(e,t,r)=>{if(!t||!o(e))return r;const s=f(t.split(/[,[\].]+?/)).reduce(((e,t)=>i(e)?e:e[t]),e);return m(s)||s===e?m(e[t])?r:e[t]:s},g=e=>"boolean"==typeof e,b=e=>/^\w*$/.test(e),_=e=>f(e.replace(/["|']|\]/g,"").split(/\.|\[/)),p=(e,t,r)=>{let s=-1;const a=b(t)?[t]:_(t),i=a.length,n=i-1;for(;++s<i;){const t=a[s];let i=r;if(s!==n){const r=e[t];i=o(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};const v="blur",h="focusout",V="change",x="onBlur",F="onChange",A="onSubmit",S="onTouched",w="all",k="max",D="min",E="maxLength",C="minLength",O="pattern",j="required",M="validate",T=e.createContext(null),N=()=>e.useContext(T);var B=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==w&&(t._proxyFormState[a]=!s||w),r&&(r[a]=!0),e[a]}});return a};const L="undefined"!=typeof window?r.useLayoutEffect:r.useEffect;function U(t){const r=N(),{control:s=r.control,disabled:a,name:i,exact:n}=t||{},[o,l]=e.useState(s._formState),u=e.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return L((()=>s._subscribe({name:i,formState:u.current,exact:n,callback:e=>{!a&&l({...s._formState,...e})}})),[i,a,n]),e.useEffect((()=>{u.current.isValid&&s._setValid(!0)}),[s]),e.useMemo((()=>B(o,s,u.current,!1)),[o,s])}var R=e=>"string"==typeof e,P=(e,t,r,s,a)=>R(e)?(s&&t.watch.add(e),y(r,e,a)):Array.isArray(e)?e.map((e=>(s&&t.watch.add(e),y(r,e)))):(s&&(t.watchAll=!0),r);function q(t){const r=N(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:o}=t||{},l=e.useRef(i),[u,d]=e.useState(s._getWatch(a,l.current));return L((()=>s._subscribe({name:a,formState:{values:!0},exact:o,callback:e=>!n&&d(P(a,s._names,e.values||s._formValues,!1,l.current))})),[a,s,n,o]),e.useEffect((()=>s._removeUnmounted())),u}function W(t){const r=N(),{name:s,disabled:a,control:i=r.control,shouldUnregister:n}=t,o=u(i._names.array,s),d=q({control:i,name:s,defaultValue:y(i._formValues,s,y(i._defaultValues,s,t.defaultValue)),exact:!0}),f=U({control:i,name:s,exact:!0}),b=e.useRef(t),_=e.useRef(i.register(s,{...t.rules,value:d,...g(t.disabled)?{disabled:t.disabled}:{}})),h=e.useMemo((()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!y(f.errors,s)},isDirty:{enumerable:!0,get:()=>!!y(f.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!y(f.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!y(f.validatingFields,s)},error:{enumerable:!0,get:()=>y(f.errors,s)}})),[f,s]),x=e.useCallback((e=>_.current.onChange({target:{value:l(e),name:s},type:V})),[s]),F=e.useCallback((()=>_.current.onBlur({target:{value:y(i._formValues,s),name:s},type:v})),[s,i._formValues]),A=e.useCallback((e=>{const t=y(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}),[i._fields,s]),S=e.useMemo((()=>({name:s,value:d,...g(a)||f.disabled?{disabled:f.disabled||a}:{},onChange:x,onBlur:F,ref:A})),[s,a,f.disabled,x,F,A,d]);return e.useEffect((()=>{const e=i._options.shouldUnregister||n;i.register(s,{...b.current.rules,...g(b.current.disabled)?{disabled:b.current.disabled}:{}});const t=(e,t)=>{const r=y(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=c(y(i._options.defaultValues,s));p(i._defaultValues,s,e),m(y(i._formValues,s))&&p(i._formValues,s,e)}return!o&&i.register(s),()=>{(o?e&&!i._state.action:e)?i.unregister(s):t(s,!1)}}),[s,i,o,n]),e.useEffect((()=>{i._setDisabledField({disabled:a,name:s})}),[a,s,i]),e.useMemo((()=>({field:S,formState:f,fieldState:h})),[S,f,h])}const I=e=>{const t={};for(const r of Object.keys(e))if(n(e[r])&&null!==e[r]){const s=I(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},$="post";var H=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},z=e=>Array.isArray(e)?e:[e],J=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},G=e=>i(e)||!n(e);function K(e,t){if(G(e)||G(t))return e===t;if(a(e)&&a(t))return e.getTime()===t.getTime();const r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;for(const i of r){const r=e[i];if(!s.includes(i))return!1;if("ref"!==i){const e=t[i];if(a(r)&&a(e)||o(r)&&o(e)||Array.isArray(r)&&Array.isArray(e)?!K(r,e):r!==e)return!1}}return!0}var Q=e=>o(e)&&!Object.keys(e).length,X=e=>"file"===e.type,Y=e=>"function"==typeof e,Z=e=>{if(!d)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},ee=e=>"select-multiple"===e.type,te=e=>"radio"===e.type,re=e=>Z(e)&&e.isConnected;function se(e,t){const r=Array.isArray(t)?t:b(t)?[t]:_(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=m(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(o(s)&&Q(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!m(e[t]))return!1;return!0}(s))&&se(e,r.slice(0,-1)),e}var ae=e=>{for(const t in e)if(Y(e[t]))return!0;return!1};function ie(e,t={}){const r=Array.isArray(e);if(o(e)||r)for(const r in e)Array.isArray(e[r])||o(e[r])&&!ae(e[r])?(t[r]=Array.isArray(e[r])?[]:{},ie(e[r],t[r])):i(e[r])||(t[r]=!0);return t}function ne(e,t,r){const s=Array.isArray(e);if(o(e)||s)for(const s in e)Array.isArray(e[s])||o(e[s])&&!ae(e[s])?m(t)||G(r[s])?r[s]=Array.isArray(e[s])?ie(e[s],[]):{...ie(e[s])}:ne(e[s],i(t)?{}:t[s],r[s]):r[s]=!K(e[s],t[s]);return r}var oe=(e,t)=>ne(e,t,ie(t));const le={value:!1,isValid:!1},ue={value:!0,isValid:!0};var de=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!m(e[0].attributes.value)?m(e[0].value)||""===e[0].value?ue:{value:e[0].value,isValid:!0}:ue:le}return le},ce=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>m(e)?e:t?""===e?NaN:e?+e:e:r&&R(e)?new Date(e):s?s(e):e;const fe={isValid:!1,value:null};var me=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),fe):fe;function ye(e){const t=e.ref;return X(t)?t.files:te(t)?me(e.refs).value:ee(t)?[...t.selectedOptions].map((({value:e})=>e)):s(t)?de(e.refs).value:ce(m(t.value)?e.ref.value:t.value,e)}var ge=e=>e instanceof RegExp,be=e=>m(e)?e:ge(e)?e.source:o(e)?ge(e.value)?e.value.source:e.value:e,_e=e=>({isOnSubmit:!e||e===A,isOnBlur:e===x,isOnChange:e===F,isOnAll:e===w,isOnTouch:e===S});const pe="AsyncFunction";var ve=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const he=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=y(e,a);if(r){const{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(he(i,t))break}else if(o(i)&&he(i,t))break}}};function Ve(e,t,r){const s=y(e,r);if(s||b(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=y(t,s),n=y(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};if(n&&n.root&&n.root.type)return{name:`${s}.root`,error:n.root};a.pop()}return{name:r}}var xe=(e,t,r)=>{const s=z(y(e,r));return p(s,"root",t[r]),p(e,r,s),e},Fe=e=>R(e);function Ae(e,t,r="validate"){if(Fe(e)||Array.isArray(e)&&e.every(Fe)||g(e)&&!e)return{type:r,message:Fe(e)?e:"",ref:t}}var Se=e=>o(e)&&!ge(e)?e:{value:e,message:""},we=async(e,t,r,a,n,l)=>{const{ref:u,refs:d,required:c,maxLength:f,minLength:b,min:_,max:p,pattern:v,validate:h,name:V,valueAsNumber:x,mount:F}=e._f,A=y(r,V);if(!F||t.has(V))return{};const S=d?d[0]:u,w=e=>{n&&S.reportValidity&&(S.setCustomValidity(g(e)?"":e||""),S.reportValidity())},T={},N=te(u),B=s(u),L=N||B,U=(x||X(u))&&m(u.value)&&m(A)||Z(u)&&""===u.value||""===A||Array.isArray(A)&&!A.length,P=H.bind(null,V,a,T),q=(e,t,r,s=E,a=C)=>{const i=e?t:r;T[V]={type:e?s:a,message:i,ref:u,...P(e?s:a,i)}};if(l?!Array.isArray(A)||!A.length:c&&(!L&&(U||i(A))||g(A)&&!A||B&&!de(d).isValid||N&&!me(d).isValid)){const{value:e,message:t}=Fe(c)?{value:!!c,message:c}:Se(c);if(e&&(T[V]={type:j,message:t,ref:S,...P(j,t)},!a))return w(t),T}if(!(U||i(_)&&i(p))){let e,t;const r=Se(p),s=Se(_);if(i(A)||isNaN(A)){const a=u.valueAsDate||new Date(A),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==u.type,o="week"==u.type;R(r.value)&&A&&(e=n?i(A)>i(r.value):o?A>r.value:a>new Date(r.value)),R(s.value)&&A&&(t=n?i(A)<i(s.value):o?A<s.value:a<new Date(s.value))}else{const a=u.valueAsNumber||(A?+A:A);i(r.value)||(e=a>r.value),i(s.value)||(t=a<s.value)}if((e||t)&&(q(!!e,r.message,s.message,k,D),!a))return w(T[V].message),T}if((f||b)&&!U&&(R(A)||l&&Array.isArray(A))){const e=Se(f),t=Se(b),r=!i(e.value)&&A.length>+e.value,s=!i(t.value)&&A.length<+t.value;if((r||s)&&(q(r,e.message,t.message),!a))return w(T[V].message),T}if(v&&!U&&R(A)){const{value:e,message:t}=Se(v);if(ge(e)&&!A.match(e)&&(T[V]={type:O,message:t,ref:u,...P(O,t)},!a))return w(t),T}if(h)if(Y(h)){const e=Ae(await h(A,r),S);if(e&&(T[V]={...e,...P(M,e.message)},!a))return w(e.message),T}else if(o(h)){let e={};for(const t in h){if(!Q(e)&&!a)break;const s=Ae(await h[t](A,r),S,t);s&&(e={...s,...P(t,s.message)},w(s.message),a&&(T[V]=e))}if(!Q(e)&&(T[V]={ref:S,...e},!a))return T}return w(!0),T};const ke={mode:A,reValidateMode:F,shouldFocusError:!0};function De(e={}){let t={...ke,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:Y(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1};const n={};let b,_=(o(t.defaultValues)||o(t.values))&&c(t.defaultValues||t.values)||{},V=t.shouldUnregister?{}:c(_),x={action:!1,mount:!1,watch:!1},F={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},A=0;const S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let k={...S};const D={array:J(),state:J()},E=t.criteriaMode===w,C=async e=>{if(!t.disabled&&(S.isValid||k.isValid||e)){const e=t.resolver?Q((await N()).errors):await B(n,!0);e!==r.isValid&&D.state.next({isValid:e})}},O=(e,s)=>{!t.disabled&&(S.isValidating||S.validatingFields||k.isValidating||k.validatingFields)&&((e||Array.from(F.mount)).forEach((e=>{e&&(s?p(r.validatingFields,e,s):se(r.validatingFields,e))})),D.state.next({validatingFields:r.validatingFields,isValidating:!Q(r.validatingFields)}))},j=(e,t,r,s)=>{const a=y(n,e);if(a){const i=y(V,e,m(r)?y(_,e):r);m(i)||s&&s.defaultChecked||t?p(V,e,t?i:ye(a._f)):q(e,i),x.mount&&C()}},M=(e,s,a,i,n)=>{let o=!1,l=!1;const u={name:e};if(!t.disabled){if(!a||i){(S.isDirty||k.isDirty)&&(l=r.isDirty,r.isDirty=u.isDirty=L(),o=l!==u.isDirty);const t=K(y(_,e),s);l=!!y(r.dirtyFields,e),t?se(r.dirtyFields,e):p(r.dirtyFields,e,!0),u.dirtyFields=r.dirtyFields,o=o||(S.dirtyFields||k.dirtyFields)&&l!==!t}if(a){const t=y(r.touchedFields,e);t||(p(r.touchedFields,e,a),u.touchedFields=r.touchedFields,o=o||(S.touchedFields||k.touchedFields)&&t!==a)}o&&n&&D.state.next(u)}return o?u:{}},T=(e,s,a,i)=>{const n=y(r.errors,e),o=(S.isValid||k.isValid)&&g(s)&&r.isValid!==s;var l;if(t.delayError&&a?(l=()=>((e,t)=>{p(r.errors,e,t),D.state.next({errors:r.errors})})(e,a),b=e=>{clearTimeout(A),A=setTimeout(l,e)},b(t.delayError)):(clearTimeout(A),b=null,a?p(r.errors,e,a):se(r.errors,e)),(a?!K(n,a):n)||!Q(i)||o){const t={...i,...o&&g(s)?{isValid:s}:{},errors:r.errors,name:e};r={...r,...t},D.state.next(t)}},N=async e=>{O(e,!0);const r=await t.resolver(V,t.context,((e,t,r,s)=>{const a={};for(const r of e){const e=y(t,r);e&&p(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||F.mount,n,t.criteriaMode,t.shouldUseNativeValidation));return O(e),r},B=async(e,s,a={valid:!0})=>{for(const n in e){const l=e[n];if(l){const{_f:e,...u}=l;if(e){const u=F.array.has(e.name),d=l._f&&(!!(i=l._f)&&!!i.validate&&!!(Y(i.validate)&&i.validate.constructor.name===pe||o(i.validate)&&Object.values(i.validate).find((e=>e.constructor.name===pe))));d&&S.validatingFields&&O([n],!0);const c=await we(l,F.disabled,V,E,t.shouldUseNativeValidation&&!s,u);if(d&&S.validatingFields&&O([n]),c[e.name]&&(a.valid=!1,s))break;!s&&(y(c,e.name)?u?xe(r.errors,c,e.name):p(r.errors,e.name,c[e.name]):se(r.errors,e.name))}!Q(u)&&await B(u,s,a)}}var i;return a.valid},L=(e,r)=>!t.disabled&&(e&&r&&p(V,e,r),!K(ae(),_)),U=(e,t,r)=>P(e,F,{...x.mount?V:m(t)?_:R(e)?{[e]:t}:t},r,t),q=(e,t,r={})=>{const a=y(n,e);let o=t;if(a){const r=a._f;r&&(!r.disabled&&p(V,e,ce(t,r)),o=Z(r.ref)&&i(t)?"":t,ee(r.ref)?[...r.ref.options].forEach((e=>e.selected=o.includes(e.value))):r.refs?s(r.ref)?r.refs.forEach((e=>{e.defaultChecked&&e.disabled||(Array.isArray(o)?e.checked=!!o.find((t=>t===e.value)):e.checked=o===e.value||!!o)})):r.refs.forEach((e=>e.checked=e.value===o)):X(r.ref)?r.ref.value="":(r.ref.value=o,r.ref.type||D.state.next({name:e,values:c(V)})))}(r.shouldDirty||r.shouldTouch)&&M(e,o,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&G(e)},W=(e,t,r)=>{for(const s in t){if(!t.hasOwnProperty(s))return;const i=t[s],l=e+"."+s,u=y(n,l);(F.array.has(e)||o(i)||u&&!u._f)&&!a(i)?W(l,i,r):q(l,i,r)}},I=(e,t,s={})=>{const a=y(n,e),o=F.array.has(e),l=c(t);p(V,e,l),o?(D.array.next({name:e,values:c(V)}),(S.isDirty||S.dirtyFields||k.isDirty||k.dirtyFields)&&s.shouldDirty&&D.state.next({name:e,dirtyFields:oe(_,V),isDirty:L(e,l)})):!a||a._f||i(l)?q(e,l,s):W(e,l,s),ve(e,F)&&D.state.next({...r}),D.state.next({name:x.mount?e:void 0,values:c(V)})},$=async e=>{x.mount=!0;const s=e.target;let i=s.name,o=!0;const u=y(n,i),d=e=>{o=Number.isNaN(e)||a(e)&&isNaN(e.getTime())||K(e,y(V,i,e))},f=_e(t.mode),m=_e(t.reValidateMode);if(u){let a,_;const x=s.type?ye(u._f):l(e),A=e.type===v||e.type===h,w=!((g=u._f).mount&&(g.required||g.min||g.max||g.maxLength||g.minLength||g.pattern||g.validate)||t.resolver||y(r.errors,i)||u._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(A,y(r.touchedFields,i),r.isSubmitted,m,f),j=ve(i,F,A);p(V,i,x),A?(u._f.onBlur&&u._f.onBlur(e),b&&b(0)):u._f.onChange&&u._f.onChange(e);const L=M(i,x,A),U=!Q(L)||j;if(!A&&D.state.next({name:i,type:e.type,values:c(V)}),w)return(S.isValid||k.isValid)&&("onBlur"===t.mode?A&&C():A||C()),U&&D.state.next({name:i,...j?{}:L});if(!A&&j&&D.state.next({...r}),t.resolver){const{errors:e}=await N([i]);if(d(x),o){const t=Ve(r.errors,n,i),s=Ve(e,n,t.name||i);a=s.error,i=s.name,_=Q(e)}}else O([i],!0),a=(await we(u,F.disabled,V,E,t.shouldUseNativeValidation))[i],O([i]),d(x),o&&(a?_=!1:(S.isValid||k.isValid)&&(_=await B(n,!0)));o&&(u._f.deps&&G(u._f.deps),T(i,_,a,L))}var g},H=(e,t)=>{if(y(r.errors,t)&&e.focus)return e.focus(),1},G=async(e,s={})=>{let a,i;const o=z(e);if(t.resolver){const t=await(async e=>{const{errors:t}=await N(e);if(e)for(const s of e){const e=y(t,s);e?p(r.errors,s,e):se(r.errors,s)}else r.errors=t;return t})(m(e)?e:o);a=Q(t),i=e?!o.some((e=>y(t,e))):a}else e?(i=(await Promise.all(o.map((async e=>{const t=y(n,e);return await B(t&&t._f?{[e]:t}:t)})))).every(Boolean),(i||r.isValid)&&C()):i=a=await B(n);return D.state.next({...!R(e)||(S.isValid||k.isValid)&&a!==r.isValid?{}:{name:e},...t.resolver||!e?{isValid:a}:{},errors:r.errors}),s.shouldFocus&&!i&&he(n,H,e?o:F.mount),i},ae=e=>{const t={...x.mount?V:_};return m(e)?t:R(e)?y(t,e):e.map((e=>y(t,e)))},ie=(e,t)=>({invalid:!!y((t||r).errors,e),isDirty:!!y((t||r).dirtyFields,e),error:y((t||r).errors,e),isValidating:!!y(r.validatingFields,e),isTouched:!!y((t||r).touchedFields,e)}),ne=(e,t,s)=>{const a=(y(n,e,{_f:{}})._f||{}).ref,i=y(r.errors,e)||{},{ref:o,message:l,type:u,...d}=i;p(r.errors,e,{...d,...t,ref:a}),D.state.next({name:e,errors:r.errors,isValid:!1}),s&&s.shouldFocus&&a&&a.focus&&a.focus()},le=e=>D.state.subscribe({next:t=>{var s,a,i;s=e.name,a=t.name,i=e.exact,s&&a&&s!==a&&!z(s).some((e=>e&&(i?e===a:e.startsWith(a)||a.startsWith(e))))||!((e,t,r,s)=>{r(e);const{name:a,...i}=e;return Q(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find((e=>t[e]===(!s||w)))})(t,e.formState||S,Se,e.reRenderRoot)||e.callback({values:{...V},...r,...t})}}).unsubscribe,ue=(e,s={})=>{for(const a of e?z(e):F.mount)F.mount.delete(a),F.array.delete(a),s.keepValue||(se(n,a),se(V,a)),!s.keepError&&se(r.errors,a),!s.keepDirty&&se(r.dirtyFields,a),!s.keepTouched&&se(r.touchedFields,a),!s.keepIsValidating&&se(r.validatingFields,a),!t.shouldUnregister&&!s.keepDefaultValue&&se(_,a);D.state.next({values:c(V)}),D.state.next({...r,...s.keepDirty?{isDirty:L()}:{}}),!s.keepIsValid&&C()},de=({disabled:e,name:t})=>{(g(e)&&x.mount||e||F.disabled.has(t))&&(e?F.disabled.add(t):F.disabled.delete(t))},fe=(e,r={})=>{let a=y(n,e);const i=g(r.disabled)||g(t.disabled);return p(n,e,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:e}},name:e,mount:!0,...r}}),F.mount.add(e),a?de({disabled:g(r.disabled)?r.disabled:t.disabled,name:e}):j(e,!0,r.value),{...i?{disabled:r.disabled||t.disabled}:{},...t.progressive?{required:!!r.required,min:be(r.min),max:be(r.max),minLength:be(r.minLength),maxLength:be(r.maxLength),pattern:be(r.pattern)}:{},name:e,onChange:$,onBlur:$,ref:i=>{if(i){fe(e,r),a=y(n,e);const t=m(i.value)&&i.querySelectorAll&&i.querySelectorAll("input,select,textarea")[0]||i,o=(e=>te(e)||s(e))(t),l=a._f.refs||[];if(o?l.find((e=>e===t)):t===a._f.ref)return;p(n,e,{_f:{...a._f,...o?{refs:[...l.filter(re),t,...Array.isArray(y(_,e))?[{}]:[]],ref:{type:t.type,name:e}}:{ref:t}}}),j(e,!1,void 0,t)}else a=y(n,e,{}),a._f&&(a._f.mount=!1),(t.shouldUnregister||r.shouldUnregister)&&(!u(F.array,e)||!x.action)&&F.unMount.add(e)}}},me=()=>t.shouldFocusError&&he(n,H,F.mount),ge=(e,s)=>async a=>{let i;a&&(a.preventDefault&&a.preventDefault(),a.persist&&a.persist());let o=c(V);if(D.state.next({isSubmitting:!0}),t.resolver){const{errors:e,values:t}=await N();r.errors=e,o=t}else await B(n);if(F.disabled.size)for(const e of F.disabled)p(o,e,void 0);if(se(r.errors,"root"),Q(r.errors)){D.state.next({errors:{}});try{await e(o,a)}catch(e){i=e}}else s&&await s({...r.errors},a),me(),setTimeout(me);if(D.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Q(r.errors)&&!i,submitCount:r.submitCount+1,errors:r.errors}),i)throw i},Fe=(e,s={})=>{const a=e?c(e):_,i=c(a),o=Q(e),l=o?_:i;if(s.keepDefaultValues||(_=a),!s.keepValues){if(s.keepDirtyValues){const e=new Set([...F.mount,...Object.keys(oe(_,V))]);for(const t of Array.from(e))y(r.dirtyFields,t)?p(l,t,y(V,t)):I(t,y(l,t))}else{if(d&&m(e))for(const e of F.mount){const t=y(n,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(Z(e)){const t=e.closest("form");if(t){t.reset();break}}}}for(const e of F.mount)I(e,y(l,e))}V=c(l),D.array.next({values:{...l}}),D.state.next({values:{...l}})}F={mount:s.keepDirtyValues?F.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},x.mount=!S.isValid||!!s.keepIsValid||!!s.keepDirtyValues,x.watch=!!t.shouldUnregister,D.state.next({submitCount:s.keepSubmitCount?r.submitCount:0,isDirty:!o&&(s.keepDirty?r.isDirty:!(!s.keepDefaultValues||K(e,_))),isSubmitted:!!s.keepIsSubmitted&&r.isSubmitted,dirtyFields:o?{}:s.keepDirtyValues?s.keepDefaultValues&&V?oe(_,V):r.dirtyFields:s.keepDefaultValues&&e?oe(_,e):s.keepDirty?r.dirtyFields:{},touchedFields:s.keepTouched?r.touchedFields:{},errors:s.keepErrors?r.errors:{},isSubmitSuccessful:!!s.keepIsSubmitSuccessful&&r.isSubmitSuccessful,isSubmitting:!1})},Ae=(e,t)=>Fe(Y(e)?e(V):e,t),Se=e=>{r={...r,...e}},De={control:{register:fe,unregister:ue,getFieldState:ie,handleSubmit:ge,setError:ne,_subscribe:le,_runSchema:N,_focusError:me,_getWatch:U,_getDirty:L,_setValid:C,_setFieldArray:(e,s=[],a,i,o=!0,l=!0)=>{if(i&&a&&!t.disabled){if(x.action=!0,l&&Array.isArray(y(n,e))){const t=a(y(n,e),i.argA,i.argB);o&&p(n,e,t)}if(l&&Array.isArray(y(r.errors,e))){const t=a(y(r.errors,e),i.argA,i.argB);o&&p(r.errors,e,t),((e,t)=>{!f(y(e,t)).length&&se(e,t)})(r.errors,e)}if((S.touchedFields||k.touchedFields)&&l&&Array.isArray(y(r.touchedFields,e))){const t=a(y(r.touchedFields,e),i.argA,i.argB);o&&p(r.touchedFields,e,t)}(S.dirtyFields||k.dirtyFields)&&(r.dirtyFields=oe(_,V)),D.state.next({name:e,isDirty:L(e,s),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else p(V,e,s)},_setDisabledField:de,_setErrors:e=>{r.errors=e,D.state.next({errors:r.errors,isValid:!1})},_getFieldArray:e=>f(y(x.mount?V:_,e,t.shouldUnregister?y(_,e,[]):[])),_reset:Fe,_resetDefaultValues:()=>Y(t.defaultValues)&&t.defaultValues().then((e=>{Ae(e,t.resetOptions),D.state.next({isLoading:!1})})),_removeUnmounted:()=>{for(const e of F.unMount){const t=y(n,e);t&&(t._f.refs?t._f.refs.every((e=>!re(e))):!re(t._f.ref))&&ue(e)}F.unMount=new Set},_disableForm:e=>{g(e)&&(D.state.next({disabled:e}),he(n,((t,r)=>{const s=y(n,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach((t=>{t.disabled=s._f.disabled||e})))}),0,!1))},_subjects:D,_proxyFormState:S,get _fields(){return n},get _formValues(){return V},get _state(){return x},set _state(e){x=e},get _defaultValues(){return _},get _names(){return F},set _names(e){F=e},get _formState(){return r},get _options(){return t},set _options(e){t={...t,...e}}},subscribe:e=>(x.mount=!0,k={...k,...e.formState},le({...e,formState:k})),trigger:G,register:fe,handleSubmit:ge,watch:(e,t)=>Y(e)?D.state.subscribe({next:r=>e(U(void 0,t),r)}):U(e,t,!0),setValue:I,getValues:ae,reset:Ae,resetField:(e,t={})=>{y(n,e)&&(m(t.defaultValue)?I(e,c(y(_,e))):(I(e,t.defaultValue),p(_,e,c(t.defaultValue))),t.keepTouched||se(r.touchedFields,e),t.keepDirty||(se(r.dirtyFields,e),r.isDirty=t.defaultValue?L(e,c(y(_,e))):L()),t.keepError||(se(r.errors,e),S.isValid&&C()),D.state.next({...r}))},clearErrors:e=>{e&&z(e).forEach((e=>se(r.errors,e))),D.state.next({errors:e?r.errors:{}})},unregister:ue,setError:ne,setFocus:(e,t={})=>{const r=y(n,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&Y(e.select)&&e.select())}},getFieldState:ie};return{...De,formControl:De}}var Ee=()=>{const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)}))},Ce=(e,t,r={})=>r.shouldFocus||m(r.shouldFocus)?r.focusName||`${e}.${m(r.focusIndex)?t:r.focusIndex}.`:"",Oe=(e,t)=>[...e,...z(t)],je=e=>Array.isArray(e)?e.map((()=>{})):void 0;function Me(e,t,r){return[...e.slice(0,t),...z(r),...e.slice(t)]}var Te=(e,t,r)=>Array.isArray(e)?(m(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],Ne=(e,t)=>[...z(t),...z(e)];var Be=(e,t)=>m(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return f(s).length?s:[]}(e,z(t).sort(((e,t)=>e-t))),Le=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]},Ue=(e,t,r)=>(e[t]=r,e);exports.Controller=e=>e.render(W(e)),exports.Form=function(t){const r=N(),[s,a]=e.useState(!1),{control:i=r.control,onSubmit:n,children:o,action:l,method:u=$,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:g,...b}=t,_=async e=>{let r=!1,s="";await i.handleSubmit((async t=>{const a=new FormData;let o="";try{o=JSON.stringify(t)}catch(e){}const m=I(i._formValues);for(const e in m)a.append(e,m[e]);if(n&&await n({data:t,event:e,method:u,formData:a,formDataJson:o}),l)try{const e=[d&&d["Content-Type"],c].some((e=>e&&e.includes("json"))),t=await fetch(String(l),{method:u,headers:{...d,...c?{"Content-Type":c}:{}},body:e?o:a});t&&(g?!g(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}}))(e),r&&t.control&&(t.control._subjects.state.next({isSubmitSuccessful:!1}),t.control.setError("root.server",{type:s}))};return e.useEffect((()=>{a(!0)}),[]),m?e.createElement(e.Fragment,null,m({submit:_})):e.createElement("form",{noValidate:s,action:l,method:u,encType:c,onSubmit:_,...b},o)},exports.FormProvider=t=>{const{children:r,...s}=t;return e.createElement(T.Provider,{value:s},r)},exports.appendErrors=H,exports.createFormControl=De,exports.get=y,exports.set=p,exports.useController=W,exports.useFieldArray=function(t){const r=N(),{control:s=r.control,name:a,keyName:i="id",shouldUnregister:n,rules:o}=t,[l,u]=e.useState(s._getFieldArray(a)),d=e.useRef(s._getFieldArray(a).map(Ee)),f=e.useRef(l),m=e.useRef(a),g=e.useRef(!1);m.current=a,f.current=l,s._names.array.add(a),o&&s.register(a,o),e.useEffect((()=>s._subjects.array.subscribe({next:({values:e,name:t})=>{if(t===m.current||!t){const t=y(e,m.current);Array.isArray(t)&&(u(t),d.current=t.map(Ee))}}}).unsubscribe),[s]);const b=e.useCallback((e=>{g.current=!0,s._setFieldArray(a,e)}),[s,a]);return e.useEffect((()=>{if(s._state.action=!1,ve(a,s._names)&&s._subjects.state.next({...s._formState}),g.current&&(!_e(s._options.mode).isOnSubmit||s._formState.isSubmitted)&&!_e(s._options.reValidateMode).isOnSubmit)if(s._options.resolver)s._runSchema([a]).then((e=>{const t=y(e.errors,a),r=y(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?p(s._formState.errors,a,t):se(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))}));else{const e=y(s._fields,a);!e||!e._f||_e(s._options.reValidateMode).isOnSubmit&&_e(s._options.mode).isOnSubmit||we(e,s._names.disabled,s._formValues,s._options.criteriaMode===w,s._options.shouldUseNativeValidation,!0).then((e=>!Q(e)&&s._subjects.state.next({errors:xe(s._formState.errors,e,a)})))}s._subjects.state.next({name:a,values:c(s._formValues)}),s._names.focus&&he(s._fields,((e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1})),s._names.focus="",s._setValid(),g.current=!1}),[l,a,s]),e.useEffect((()=>(!y(s._formValues,a)&&s._setFieldArray(a),()=>{s._options.shouldUnregister||n?s.unregister(a):((e,t)=>{const r=y(s._fields,e);r&&r._f&&(r._f.mount=t)})(a,!1)})),[a,s,i,n]),{swap:e.useCallback(((e,t)=>{const r=s._getFieldArray(a);Le(r,e,t),Le(d.current,e,t),b(r),u(r),s._setFieldArray(a,r,Le,{argA:e,argB:t},!1)}),[b,a,s]),move:e.useCallback(((e,t)=>{const r=s._getFieldArray(a);Te(r,e,t),Te(d.current,e,t),b(r),u(r),s._setFieldArray(a,r,Te,{argA:e,argB:t},!1)}),[b,a,s]),prepend:e.useCallback(((e,t)=>{const r=z(c(e)),i=Ne(s._getFieldArray(a),r);s._names.focus=Ce(a,0,t),d.current=Ne(d.current,r.map(Ee)),b(i),u(i),s._setFieldArray(a,i,Ne,{argA:je(e)})}),[b,a,s]),append:e.useCallback(((e,t)=>{const r=z(c(e)),i=Oe(s._getFieldArray(a),r);s._names.focus=Ce(a,i.length-1,t),d.current=Oe(d.current,r.map(Ee)),b(i),u(i),s._setFieldArray(a,i,Oe,{argA:je(e)})}),[b,a,s]),remove:e.useCallback((e=>{const t=Be(s._getFieldArray(a),e);d.current=Be(d.current,e),b(t),u(t),!Array.isArray(y(s._fields,a))&&p(s._fields,a,void 0),s._setFieldArray(a,t,Be,{argA:e})}),[b,a,s]),insert:e.useCallback(((e,t,r)=>{const i=z(c(t)),n=Me(s._getFieldArray(a),e,i);s._names.focus=Ce(a,e,r),d.current=Me(d.current,e,i.map(Ee)),b(n),u(n),s._setFieldArray(a,n,Me,{argA:e,argB:je(t)})}),[b,a,s]),update:e.useCallback(((e,t)=>{const r=c(t),i=Ue(s._getFieldArray(a),e,r);d.current=[...i].map(((t,r)=>t&&r!==e?d.current[r]:Ee())),b(i),u([...i]),s._setFieldArray(a,i,Ue,{argA:e,argB:r},!0,!1)}),[b,a,s]),replace:e.useCallback((e=>{const t=z(c(e));d.current=t.map(Ee),b([...t]),u([...t]),s._setFieldArray(a,[...t],(e=>e),{},!0,!1)}),[b,a,s]),fields:e.useMemo((()=>l.map(((e,t)=>({...e,[i]:d.current[t]||Ee()})))),[l,i])}},exports.useForm=function(t={}){const r=e.useRef(void 0),s=e.useRef(void 0),[a,i]=e.useState({isDirty:!1,isValidating:!1,isLoading:Y(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,isReady:!1,defaultValues:Y(t.defaultValues)?void 0:t.defaultValues});r.current||(r.current={...t.formControl?t.formControl:De(t),formState:a},t.formControl&&t.defaultValues&&!Y(t.defaultValues)&&t.formControl.reset(t.defaultValues,t.resetOptions));const n=r.current.control;return n._options=t,L((()=>{const e=n._subscribe({formState:n._proxyFormState,callback:()=>i({...n._formState}),reRenderRoot:!0});return i((e=>({...e,isReady:!0}))),n._formState.isReady=!0,e}),[n]),e.useEffect((()=>n._disableForm(t.disabled)),[n,t.disabled]),e.useEffect((()=>{t.mode&&(n._options.mode=t.mode),t.reValidateMode&&(n._options.reValidateMode=t.reValidateMode)}),[n,t.mode,t.reValidateMode]),e.useEffect((()=>{t.errors&&(n._setErrors(t.errors),n._focusError())}),[n,t.errors]),e.useEffect((()=>{t.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})}),[n,t.shouldUnregister]),e.useEffect((()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}}),[n,a.isDirty]),e.useEffect((()=>{t.values&&!K(t.values,s.current)?(n._reset(t.values,n._options.resetOptions),s.current=t.values,i((e=>({...e})))):n._resetDefaultValues()}),[n,t.values]),e.useEffect((()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()})),r.current.formState=B(a,n),r.current},exports.useFormContext=N,exports.useFormState=U,exports.useWatch=q;
//# sourceMappingURL=index.cjs.js.map
