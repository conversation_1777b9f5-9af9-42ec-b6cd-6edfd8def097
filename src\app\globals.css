@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-fira-code);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  --font-inter: 'Inter', system-ui, sans-serif;
  --font-fira-code: 'Fira Code', 'Consolas', monospace;

  /* Light theme colors */
  --background: 255 255 255;
  --foreground: 15 23 42;
  --card: 255 255 255;
  --card-foreground: 15 23 42;
  --popover: 255 255 255;
  --popover-foreground: 15 23 42;
  --primary: 99 102 241;
  --primary-foreground: 255 255 255;
  --secondary: 241 245 249;
  --secondary-foreground: 15 23 42;
  --muted: 248 250 252;
  --muted-foreground: 100 116 139;
  --accent: 241 245 249;
  --accent-foreground: 15 23 42;
  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;
  --border: 226 232 240;
  --input: 226 232 240;
  --ring: 99 102 241;

  /* Custom gradient colors */
  --gradient-from: 99 102 241;
  --gradient-to: 139 92 246;

  /* Chart colors */
  --chart-1: 99 102 241;
  --chart-2: 16 185 129;
  --chart-3: 245 158 11;
  --chart-4: 239 68 68;
  --chart-5: 139 92 246;
}

.dark {
  /* Dark theme colors */
  --background: 2 6 23;
  --foreground: 248 250 252;
  --card: 15 23 42;
  --card-foreground: 248 250 252;
  --popover: 15 23 42;
  --popover-foreground: 248 250 252;
  --primary: 129 140 248;
  --primary-foreground: 15 23 42;
  --secondary: 30 41 59;
  --secondary-foreground: 248 250 252;
  --muted: 30 41 59;
  --muted-foreground: 148 163 184;
  --accent: 30 41 59;
  --accent-foreground: 248 250 252;
  --destructive: 248 113 113;
  --destructive-foreground: 15 23 42;
  --border: 30 41 59;
  --input: 30 41 59;
  --ring: 129 140 248;

  /* Custom gradient colors for dark theme */
  --gradient-from: 129 140 248;
  --gradient-to: 167 139 250;

  /* Chart colors for dark theme */
  --chart-1: 129 140 248;
  --chart-2: 52 211 153;
  --chart-3: 251 191 36;
  --chart-4: 248 113 113;
  --chart-5: 167 139 250;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground;
  }
}

@layer components {
  /* Glass morphism effect */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .glass-dark {
    @apply backdrop-blur-md bg-black/10 border border-white/10;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent;
  }

  /* Animated gradient background */
  .gradient-bg {
    background: linear-gradient(-45deg, #6366f1, #8b5cf6, #06b6d4, #10b981);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Floating animation */
  .float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  /* Glow effect */
  .glow {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }

  .glow-hover:hover {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.5);
    transition: box-shadow 0.3s ease;
  }

  /* Neumorphism */
  .neumorphism {
    background: linear-gradient(145deg, #f1f5f9, #e2e8f0);
    box-shadow: 20px 20px 60px #d1d5db, -20px -20px 60px #ffffff;
  }

  .neumorphism-dark {
    background: linear-gradient(145deg, #1e293b, #0f172a);
    box-shadow: 20px 20px 60px #0a0f1a, -20px -20px 60px #2c3e50;
  }

  /* Custom button styles */
  .btn-gradient {
    @apply bg-gradient-to-r from-primary to-purple-600 text-white font-medium px-6 py-3 rounded-lg;
    @apply hover:from-primary/90 hover:to-purple-600/90 transition-all duration-300;
    @apply shadow-lg hover:shadow-xl transform hover:-translate-y-1;
  }
}
