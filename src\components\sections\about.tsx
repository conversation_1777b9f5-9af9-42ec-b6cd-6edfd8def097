"use client";

import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { skills } from "@/data/skills";
import { personalInfo } from "@/data/social";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      delayChildren: 0.3,
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
    },
  },
};

export function About() {
  const skillCategories = [
    { name: "Frontend", skills: skills.filter(s => s.category === "frontend") },
    { name: "Backend", skills: skills.filter(s => s.category === "backend") },
    { name: "Database", skills: skills.filter(s => s.category === "database") },
    { name: "DevOps", skills: skills.filter(s => s.category === "devops") },
    { name: "Design", skills: skills.filter(s => s.category === "design") },
    { name: "Other", skills: skills.filter(s => s.category === "other") },
  ];

  return (
    <section id="about" className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
              About <span className="gradient-text">Me</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Passionate about creating digital experiences that make a difference
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-start">
            {/* About Content */}
            <motion.div variants={itemVariants}>
              <Card className="glass border-0 shadow-xl">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold mb-6 gradient-text">My Story</h3>
                  <div className="space-y-4 text-muted-foreground leading-relaxed">
                    <p>
                      I'm a passionate full-stack developer with over 5 years of experience 
                      building modern web applications. My journey started with curiosity about 
                      how websites work, and it has evolved into a deep love for creating 
                      digital experiences that solve real-world problems.
                    </p>
                    <p>
                      I specialize in React, Next.js, and Node.js, but I'm always eager to 
                      learn new technologies and frameworks. I believe in writing clean, 
                      maintainable code and following best practices to deliver high-quality 
                      solutions.
                    </p>
                    <p>
                      When I'm not coding, you can find me exploring new technologies, 
                      contributing to open-source projects, or sharing my knowledge through 
                      blog posts and mentoring other developers.
                    </p>
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 gap-4 mt-8">
                    <div className="text-center p-4 rounded-lg bg-primary/10">
                      <div className="text-2xl font-bold gradient-text">50+</div>
                      <div className="text-sm text-muted-foreground">Projects Completed</div>
                    </div>
                    <div className="text-center p-4 rounded-lg bg-primary/10">
                      <div className="text-2xl font-bold gradient-text">5+</div>
                      <div className="text-sm text-muted-foreground">Years Experience</div>
                    </div>
                    <div className="text-center p-4 rounded-lg bg-primary/10">
                      <div className="text-2xl font-bold gradient-text">100+</div>
                      <div className="text-sm text-muted-foreground">Happy Clients</div>
                    </div>
                    <div className="text-center p-4 rounded-lg bg-primary/10">
                      <div className="text-2xl font-bold gradient-text">24/7</div>
                      <div className="text-sm text-muted-foreground">Support</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Skills */}
            <motion.div variants={itemVariants}>
              <Card className="glass border-0 shadow-xl">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold mb-6 gradient-text">Skills & Technologies</h3>
                  
                  <div className="space-y-6">
                    {skillCategories.map((category) => (
                      <div key={category.name}>
                        <h4 className="text-lg font-semibold mb-3 text-foreground">
                          {category.name}
                        </h4>
                        <div className="space-y-3">
                          {category.skills.slice(0, 4).map((skill) => (
                            <div key={skill.name}>
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-sm font-medium flex items-center gap-2">
                                  <span>{skill.icon}</span>
                                  {skill.name}
                                </span>
                                <span className="text-sm text-muted-foreground">
                                  {skill.level}%
                                </span>
                              </div>
                              <Progress value={skill.level} className="h-2" />
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* All Skills as Badges */}
                  <div className="mt-8">
                    <h4 className="text-lg font-semibold mb-4 text-foreground">
                      All Technologies
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {skills.map((skill) => (
                        <Badge
                          key={skill.name}
                          variant="secondary"
                          className="hover:bg-primary hover:text-primary-foreground transition-colors cursor-default"
                        >
                          <span className="mr-1">{skill.icon}</span>
                          {skill.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
