"use client";

import { motion } from "framer-motion";
import { Play, Eye, ThumbsUp, Calendar, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";

const youtubeVideos = [
  {
    id: "1",
    title: "Building a Modern Portfolio with Next.js & Tailwind CSS",
    description: "Complete tutorial on creating a responsive portfolio website using Next.js, TypeScript, and Tailwind CSS with modern animations.",
    thumbnail: "/api/placeholder/400/225",
    views: "15.2K",
    likes: "892",
    duration: "28:45",
    publishedAt: "2 weeks ago",
    url: "https://youtube.com/watch?v=example1"
  },
  {
    id: "2",
    title: "React Hooks Explained: useState, useEffect & Custom Hooks",
    description: "Deep dive into React Hooks with practical examples and best practices for modern React development.",
    thumbnail: "/api/placeholder/400/225",
    views: "23.8K",
    likes: "1.2K",
    duration: "35:12",
    publishedAt: "1 month ago",
    url: "https://youtube.com/watch?v=example2"
  },
  {
    id: "3",
    title: "Full Stack MERN App: From Concept to Deployment",
    description: "Build and deploy a complete MERN stack application with authentication, CRUD operations, and modern UI.",
    thumbnail: "/api/placeholder/400/225",
    views: "31.5K",
    likes: "1.8K",
    duration: "1:42:30",
    publishedAt: "2 months ago",
    url: "https://youtube.com/watch?v=example3"
  },
  {
    id: "4",
    title: "CSS Grid vs Flexbox: When to Use What?",
    description: "Comprehensive comparison of CSS Grid and Flexbox with real-world examples and use cases.",
    thumbnail: "/api/placeholder/400/225",
    views: "18.7K",
    likes: "956",
    duration: "22:18",
    publishedAt: "3 months ago",
    url: "https://youtube.com/watch?v=example4"
  },
  {
    id: "5",
    title: "JavaScript ES6+ Features Every Developer Should Know",
    description: "Modern JavaScript features including arrow functions, destructuring, async/await, and more.",
    thumbnail: "/api/placeholder/400/225",
    views: "27.3K",
    likes: "1.5K",
    duration: "41:22",
    publishedAt: "4 months ago",
    url: "https://youtube.com/watch?v=example5"
  },
  {
    id: "6",
    title: "UI/UX Design Principles for Developers",
    description: "Essential design principles that every developer should understand to create better user interfaces.",
    thumbnail: "/api/placeholder/400/225",
    views: "12.9K",
    likes: "743",
    duration: "33:55",
    publishedAt: "5 months ago",
    url: "https://youtube.com/watch?v=example6"
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6
    }
  }
};

export function YouTube() {
  return (
    <section id="youtube" className="py-20 bg-gradient-to-br from-slate-800 via-slate-900 to-slate-800">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl lg:text-5xl font-bold mb-6"
          >
            <span className="bg-gradient-to-r from-red-400 via-pink-400 to-purple-500 bg-clip-text text-transparent">
              YouTube Channel
            </span>
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-lg text-slate-300 max-w-2xl mx-auto mb-8"
          >
            Educational content about web development, programming tutorials, and tech insights
          </motion.p>
          <motion.div variants={itemVariants}>
            <Button
              size="lg"
              className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white font-semibold px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105"
            >
              <Play className="w-5 h-5 mr-2" />
              Subscribe to Channel
            </Button>
          </motion.div>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {youtubeVideos.map((video) => (
            <motion.div
              key={video.id}
              variants={itemVariants}
              whileHover={{ scale: 1.02 }}
              className="bg-white/5 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/10 hover:border-white/20 transition-all duration-300 group"
            >
              <div className="relative">
                <div className="aspect-video bg-gradient-to-br from-slate-700 to-slate-800 flex items-center justify-center">
                  <Play className="w-16 h-16 text-white/60" />
                </div>
                <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                  {video.duration}
                </div>
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Button
                    size="sm"
                    className="bg-red-500 hover:bg-red-600 text-white rounded-full"
                  >
                    <Play className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2 group-hover:text-red-400 transition-colors">
                  {video.title}
                </h3>
                <p className="text-slate-400 text-sm mb-4 line-clamp-2">
                  {video.description}
                </p>

                <div className="flex items-center justify-between text-xs text-slate-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <Eye className="w-3 h-3 mr-1" />
                      {video.views}
                    </div>
                    <div className="flex items-center">
                      <ThumbsUp className="w-3 h-3 mr-1" />
                      {video.likes}
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {video.publishedAt}
                  </div>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full border-white/20 text-white hover:bg-white/10 hover:border-red-400 transition-all duration-300"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Watch Video
                </Button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          variants={itemVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Button
            variant="outline"
            size="lg"
            className="border-white/20 text-white hover:bg-white/10 hover:border-red-400 transition-all duration-300"
          >
            View All Videos
            <ExternalLink className="w-5 h-5 ml-2" />
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
