import { Experience, Education } from "@/types";

export const experiences: Experience[] = [
  {
    id: "1",
    company: "TechCorp Solutions",
    position: "Senior Full Stack Developer",
    duration: "2022 - Present",
    description: [
      "Led development of enterprise-level web applications using React, Next.js, and Node.js",
      "Architected and implemented microservices infrastructure serving 100k+ daily active users",
      "Mentored junior developers and established coding standards and best practices",
      "Collaborated with cross-functional teams to deliver projects 20% ahead of schedule",
      "Optimized application performance resulting in 40% faster load times"
    ],
    technologies: ["React", "Next.js", "TypeScript", "Node.js", "PostgreSQL", "AWS", "Docker"],
    logo: "/companies/techcorp.png"
  },
  {
    id: "2",
    company: "StartupXYZ",
    position: "Full Stack Developer",
    duration: "2020 - 2022",
    description: [
      "Developed and maintained multiple client projects using modern web technologies",
      "Built responsive web applications with React, Vue.js, and Angular",
      "Implemented RESTful APIs and GraphQL endpoints using Node.js and Python",
      "Worked closely with designers to create pixel-perfect user interfaces",
      "Participated in agile development processes and sprint planning"
    ],
    technologies: ["React", "Vue.js", "Angular", "Node.js", "Python", "MongoDB", "Firebase"],
    logo: "/companies/startupxyz.png"
  },
  {
    id: "3",
    company: "Digital Agency Pro",
    position: "Frontend Developer",
    duration: "2019 - 2020",
    description: [
      "Created responsive websites and web applications for various clients",
      "Collaborated with design team to implement modern UI/UX designs",
      "Optimized websites for performance and SEO best practices",
      "Maintained and updated existing client websites",
      "Worked with CMS platforms like WordPress and Drupal"
    ],
    technologies: ["HTML5", "CSS3", "JavaScript", "jQuery", "WordPress", "Sass", "Bootstrap"],
    logo: "/companies/digitalagency.png"
  },
  {
    id: "4",
    company: "FreelanceWork",
    position: "Web Developer",
    duration: "2018 - 2019",
    description: [
      "Provided web development services to small businesses and startups",
      "Built custom websites using HTML, CSS, JavaScript, and PHP",
      "Integrated third-party APIs and payment gateways",
      "Managed client relationships and project timelines",
      "Delivered projects on time and within budget"
    ],
    technologies: ["HTML5", "CSS3", "JavaScript", "PHP", "MySQL", "WordPress"],
    logo: "/companies/freelance.png"
  }
];

export const education: Education[] = [
  {
    id: "1",
    institution: "University of Technology",
    degree: "Bachelor of Science",
    field: "Computer Science",
    duration: "2015 - 2019",
    description: "Focused on software engineering, algorithms, and data structures. Graduated with honors.",
    gpa: "3.8/4.0"
  },
  {
    id: "2",
    institution: "Tech Institute",
    degree: "Certificate",
    field: "Full Stack Web Development",
    duration: "2018",
    description: "Intensive bootcamp covering modern web development technologies and best practices."
  },
  {
    id: "3",
    institution: "Online Learning Platform",
    degree: "Certificate",
    field: "Cloud Computing with AWS",
    duration: "2021",
    description: "Comprehensive course covering AWS services, cloud architecture, and DevOps practices."
  }
];
