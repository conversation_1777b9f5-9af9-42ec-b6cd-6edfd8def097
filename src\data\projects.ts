import { Project } from "@/types";

export const projects: Project[] = [
  {
    id: "1",
    title: "E-Commerce Platform",
    description: "A full-stack e-commerce platform with modern UI/UX, payment integration, and admin dashboard.",
    longDescription: "Built with Next.js, TypeScript, and Tailwind CSS. Features include user authentication, product management, shopping cart, payment processing with Stripe, order tracking, and comprehensive admin dashboard. Implemented with PostgreSQL database and deployed on Vercel.",
    image: "/projects/ecommerce.jpg",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "PostgreSQL", "Stripe", "Prisma"],
    githubUrl: "https://github.com/yourusername/ecommerce-platform",
    liveUrl: "https://ecommerce-demo.vercel.app",
    category: "web",
    featured: true,
    status: "completed"
  },
  {
    id: "2",
    title: "AI Chat Application",
    description: "Real-time chat application with AI-powered responses and modern messaging features.",
    longDescription: "A sophisticated chat application built with React, Socket.io, and OpenAI API. Features include real-time messaging, AI-powered responses, file sharing, emoji reactions, and user presence indicators. Implemented with MongoDB for message storage and Redis for session management.",
    image: "/projects/ai-chat.jpg",
    technologies: ["React", "Node.js", "Socket.io", "OpenAI API", "MongoDB", "Redis"],
    githubUrl: "https://github.com/yourusername/ai-chat-app",
    liveUrl: "https://ai-chat-demo.vercel.app",
    category: "ai",
    featured: true,
    status: "completed"
  },
  {
    id: "3",
    title: "Task Management Dashboard",
    description: "Collaborative task management tool with team features and project tracking.",
    longDescription: "A comprehensive project management dashboard built with Vue.js and Laravel. Features include task creation and assignment, team collaboration, project timelines, file attachments, and detailed analytics. Integrated with third-party tools like Slack and GitHub.",
    image: "/projects/task-manager.jpg",
    technologies: ["Vue.js", "Laravel", "MySQL", "Docker", "AWS"],
    githubUrl: "https://github.com/yourusername/task-manager",
    liveUrl: "https://taskmanager-demo.vercel.app",
    category: "web",
    featured: false,
    status: "completed"
  },
  {
    id: "4",
    title: "Mobile Fitness App",
    description: "Cross-platform mobile app for fitness tracking and workout planning.",
    longDescription: "A React Native application for fitness enthusiasts. Features include workout tracking, exercise library, progress analytics, social features, and integration with wearable devices. Built with Firebase for backend services and real-time data synchronization.",
    image: "/projects/fitness-app.jpg",
    technologies: ["React Native", "Firebase", "TypeScript", "Expo"],
    githubUrl: "https://github.com/yourusername/fitness-app",
    category: "mobile",
    featured: true,
    status: "in-progress"
  },
  {
    id: "5",
    title: "Data Visualization Platform",
    description: "Interactive dashboard for data analysis and visualization with real-time updates.",
    longDescription: "A powerful data visualization platform built with D3.js and React. Features include interactive charts, real-time data updates, custom dashboard creation, data export capabilities, and integration with various data sources including APIs and databases.",
    image: "/projects/data-viz.jpg",
    technologies: ["React", "D3.js", "Python", "FastAPI", "PostgreSQL"],
    githubUrl: "https://github.com/yourusername/data-viz-platform",
    liveUrl: "https://dataviz-demo.vercel.app",
    category: "web",
    featured: false,
    status: "completed"
  },
  {
    id: "6",
    title: "Blockchain Voting System",
    description: "Secure and transparent voting system built on blockchain technology.",
    longDescription: "A decentralized voting application built with Solidity smart contracts and React frontend. Features include voter registration, secure ballot casting, real-time vote counting, and transparent result verification. Deployed on Ethereum testnet with MetaMask integration.",
    image: "/projects/blockchain-voting.jpg",
    technologies: ["Solidity", "React", "Web3.js", "Ethereum", "IPFS"],
    githubUrl: "https://github.com/yourusername/blockchain-voting",
    category: "web",
    featured: false,
    status: "planned"
  }
];
