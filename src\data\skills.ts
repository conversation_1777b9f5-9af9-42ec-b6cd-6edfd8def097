import { Skill } from "@/types";

export const skills: Skill[] = [
  // Frontend
  {
    name: "React",
    level: 95,
    category: "frontend",
    icon: "⚛️"
  },
  {
    name: "Next.js",
    level: 90,
    category: "frontend",
    icon: "▲"
  },
  {
    name: "TypeScript",
    level: 88,
    category: "frontend",
    icon: "📘"
  },
  {
    name: "Vue.js",
    level: 85,
    category: "frontend",
    icon: "💚"
  },
  {
    name: "Tailwind CSS",
    level: 92,
    category: "frontend",
    icon: "🎨"
  },
  {
    name: "JavaScript",
    level: 93,
    category: "frontend",
    icon: "🟨"
  },
  {
    name: "HTML5",
    level: 95,
    category: "frontend",
    icon: "🌐"
  },
  {
    name: "CSS3",
    level: 90,
    category: "frontend",
    icon: "🎨"
  },
  {
    name: "Sass/SCSS",
    level: 85,
    category: "frontend",
    icon: "💄"
  },
  {
    name: "Framer Motion",
    level: 80,
    category: "frontend",
    icon: "🎭"
  },

  // Backend
  {
    name: "Node.js",
    level: 88,
    category: "backend",
    icon: "🟢"
  },
  {
    name: "Express.js",
    level: 85,
    category: "backend",
    icon: "🚂"
  },
  {
    name: "Python",
    level: 82,
    category: "backend",
    icon: "🐍"
  },
  {
    name: "Django",
    level: 78,
    category: "backend",
    icon: "🎸"
  },
  {
    name: "FastAPI",
    level: 75,
    category: "backend",
    icon: "⚡"
  },
  {
    name: "Laravel",
    level: 70,
    category: "backend",
    icon: "🔴"
  },
  {
    name: "GraphQL",
    level: 72,
    category: "backend",
    icon: "🔗"
  },
  {
    name: "REST APIs",
    level: 90,
    category: "backend",
    icon: "🔌"
  },

  // Database
  {
    name: "PostgreSQL",
    level: 85,
    category: "database",
    icon: "🐘"
  },
  {
    name: "MongoDB",
    level: 80,
    category: "database",
    icon: "🍃"
  },
  {
    name: "MySQL",
    level: 82,
    category: "database",
    icon: "🐬"
  },
  {
    name: "Redis",
    level: 75,
    category: "database",
    icon: "🔴"
  },
  {
    name: "Prisma",
    level: 78,
    category: "database",
    icon: "⚡"
  },

  // DevOps
  {
    name: "Docker",
    level: 80,
    category: "devops",
    icon: "🐳"
  },
  {
    name: "AWS",
    level: 75,
    category: "devops",
    icon: "☁️"
  },
  {
    name: "Vercel",
    level: 88,
    category: "devops",
    icon: "▲"
  },
  {
    name: "Git",
    level: 92,
    category: "devops",
    icon: "📚"
  },
  {
    name: "GitHub Actions",
    level: 70,
    category: "devops",
    icon: "⚙️"
  },
  {
    name: "Nginx",
    level: 68,
    category: "devops",
    icon: "🌐"
  },

  // Design
  {
    name: "Figma",
    level: 85,
    category: "design",
    icon: "🎨"
  },
  {
    name: "Adobe XD",
    level: 75,
    category: "design",
    icon: "🎭"
  },
  {
    name: "UI/UX Design",
    level: 80,
    category: "design",
    icon: "✨"
  },
  {
    name: "Responsive Design",
    level: 92,
    category: "design",
    icon: "📱"
  },

  // Other
  {
    name: "React Native",
    level: 75,
    category: "other",
    icon: "📱"
  },
  {
    name: "Electron",
    level: 65,
    category: "other",
    icon: "⚡"
  },
  {
    name: "Socket.io",
    level: 78,
    category: "other",
    icon: "🔌"
  },
  {
    name: "WebRTC",
    level: 60,
    category: "other",
    icon: "📹"
  },
  {
    name: "Blockchain",
    level: 55,
    category: "other",
    icon: "⛓️"
  },
  {
    name: "Machine Learning",
    level: 50,
    category: "other",
    icon: "🤖"
  }
];
