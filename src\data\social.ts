import { SocialLink, NavItem } from "@/types";

export const socialLinks: SocialLink[] = [
  {
    name: "GitHub",
    url: "https://github.com/bhupenderyadav",
    icon: "Github"
  },
  {
    name: "LinkedIn",
    url: "https://linkedin.com/in/bhupenderyadav",
    icon: "Linkedin"
  },
  {
    name: "Twitter",
    url: "https://twitter.com/bhupenderyadav",
    icon: "Twitter"
  },
  {
    name: "Email",
    url: "mailto:<EMAIL>",
    icon: "Mail"
  },
  {
    name: "Instagram",
    url: "https://instagram.com/bhupenderyadav",
    icon: "Instagram"
  },
  {
    name: "YouTube",
    url: "https://youtube.com/@bhupenderyadav",
    icon: "Youtube"
  }
];

export const navigationItems: NavItem[] = [
  {
    name: "Home",
    href: "#home",
    icon: "Home"
  },
  {
    name: "About",
    href: "#about",
    icon: "User"
  },
  {
    name: "Projects",
    href: "#projects",
    icon: "FolderOpen"
  },
  {
    name: "Experience",
    href: "#experience",
    icon: "Briefcase"
  },
  {
    name: "Contact",
    href: "#contact",
    icon: "MessageCircle"
  }
];

export const personalInfo = {
  name: "Bhupender Yadav",
  title: "Full Stack Developer & UI UX Designer",
  subtitle: "Building digital experiences that matter",
  bio: "Passionate web developer with expertise in building dynamic and responsive websites. Skilled in JavaScript, React js, Tailwind CSS and with a focus on user-friendly design. Enthusiastic about technology, fitness, and content creation.",
  location: "India",
  email: "<EMAIL>",
  phone: "+91 7206110977",
  website: "https://bhupenderyadav.com",
  resumeUrl: "/resume.pdf",
  avatar: "/bhupi.jpg"
};
