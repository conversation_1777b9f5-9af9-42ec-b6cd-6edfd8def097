import { SocialLink, NavItem } from "@/types";

export const socialLinks: SocialLink[] = [
  {
    name: "GitHub",
    url: "https://github.com/bhupenderyadav",
    icon: "Github"
  },
  {
    name: "LinkedIn",
    url: "https://linkedin.com/in/bhupenderyadav",
    icon: "Linkedin"
  },
  {
    name: "Twitter",
    url: "https://twitter.com/bhupenderyadav",
    icon: "Twitter"
  },
  {
    name: "Email",
    url: "mailto:<EMAIL>",
    icon: "Mail"
  },
  {
    name: "Instagram",
    url: "https://instagram.com/bhupenderyadav",
    icon: "Instagram"
  },
  {
    name: "YouTube",
    url: "https://youtube.com/@bhupenderyadav",
    icon: "Youtube"
  }
];

export const navigationItems: NavItem[] = [
  {
    name: "Home",
    href: "#home",
    icon: "Home"
  },
  {
    name: "About",
    href: "#about",
    icon: "User"
  },
  {
    name: "Skills",
    href: "#skills",
    icon: "Code"
  },
  {
    name: "Projects",
    href: "#projects",
    icon: "FolderOpen"
  },
  {
    name: "YouTube",
    href: "#youtube",
    icon: "Youtube"
  },
  {
    name: "Experience",
    href: "#experience",
    icon: "Briefcase"
  },
  {
    name: "Contact",
    href: "#contact",
    icon: "MessageCircle"
  }
];

export const personalInfo = {
  name: "Bhupender Yadav",
  title: "Full Stack Developer & UI UX Designer",
  subtitle: "Building digital experiences that matter",
  bio: "I'm a passionate full stack developer with 5+ years of experience creating modern web applications. I love turning complex problems into simple, beautiful, and intuitive solutions.",
  location: "India",
  email: "<EMAIL>",
  phone: "+91 7206110977",
  website: "https://bhupenderyadav.com",
  resumeUrl: "/resume.pdf",
  avatar: "/bhupi.jpg"
};
