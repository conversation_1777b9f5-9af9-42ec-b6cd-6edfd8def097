import { SocialLink, NavItem } from "@/types";

export const socialLinks: SocialLink[] = [
  {
    name: "GitHub",
    url: "https://github.com/yourusername",
    icon: "Github"
  },
  {
    name: "LinkedIn",
    url: "https://linkedin.com/in/yourusername",
    icon: "Linkedin"
  },
  {
    name: "Twitter",
    url: "https://twitter.com/yourusername",
    icon: "Twitter"
  },
  {
    name: "<PERSON><PERSON>",
    url: "mailto:<EMAIL>",
    icon: "Mail"
  },
  {
    name: "Instagram",
    url: "https://instagram.com/yourusername",
    icon: "Instagram"
  },
  {
    name: "YouTube",
    url: "https://youtube.com/@yourusername",
    icon: "Youtube"
  }
];

export const navigationItems: NavItem[] = [
  {
    name: "Home",
    href: "#home",
    icon: "Home"
  },
  {
    name: "About",
    href: "#about",
    icon: "User"
  },
  {
    name: "Projects",
    href: "#projects",
    icon: "FolderOpen"
  },
  {
    name: "Experience",
    href: "#experience",
    icon: "Briefcase"
  },
  {
    name: "Contact",
    href: "#contact",
    icon: "MessageCircle"
  }
];

export const personalInfo = {
  name: "Your Name",
  title: "Full Stack Developer",
  subtitle: "Building digital experiences that matter",
  bio: "I'm a passionate full stack developer with 5+ years of experience creating modern web applications. I love turning complex problems into simple, beautiful, and intuitive solutions.",
  location: "San Francisco, CA",
  email: "<EMAIL>",
  phone: "+****************",
  website: "https://yourwebsite.com",
  resumeUrl: "/resume.pdf",
  avatar: "/avatar.jpg"
};
